{"name": "forum-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --config jest.config.cjs", "realtime": "tsx server/realtime-server.ts", "dev:full": "npm-run-all --parallel realtime dev", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "db:export-seed": "tsx scripts/export-seed.ts", "db:seed": "tsx prisma/seed.ts", "db:backup": "bash scripts/backup-db.sh", "docker:build": "docker build -t forum-app .", "docker:build:dev": "docker build -f Dockerfile.dev -t forum-app:dev .", "docker:up": "docker-compose up -d", "docker:up:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:down": "docker-compose down", "docker:down:dev": "docker-compose -f docker-compose.dev.yml down", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v --remove-orphans && docker system prune -f", "dev:local": "next dev", "env:use:dev": "cp .env.dev .env", "env:use:prod": "cp .env.prod .env", "env:print": "echo CURRENT .env && (grep -E '^(NEXTAUTH_URL|NODE_ENV|DATABASE_URL|REDIS_HOST)' .env || echo .env missing)", "env:gen:prod": "tsx scripts/gen-prod-env.ts", "execute-permissions": "echo Ensure scripts/gen-prod-env.ts has execute permissions", "seed:admin": "tsx scripts/seed-admin.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.13.0", "@socket.io/redis-adapter": "^8.3.0", "@socket.io/redis-emitter": "^5.1.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "dotenv": "^16.4.5", "ioredis": "^5.4.1", "isomorphic-dompurify": "^2.13.0", "marked": "^12.0.2", "next": "15.4.5", "next-auth": "^4.24.11", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "zod": "^4.0.14", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.12", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/supertest": "^6.0.3", "@wixc3/react-board": "^4.6.2", "autoprefixer": "^10.4.21", "daisyui": "^5.1.4", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.6", "supertest": "^7.1.4", "tailwindcss": "^4.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "tsx": "^4.7.0", "typescript": "^5"}}