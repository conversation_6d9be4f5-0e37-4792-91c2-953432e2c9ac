"use client";

import { useSession, signIn, signOut } from "next-auth/react";
import Link from "next/link";
import { useState, useEffect } from "react";
import Image from 'next/image';
import ThemeSelector from "./ThemeSelector";

interface NavbarProps {
  onToggleSidebar?: () => void;
  isSidebarOpen?: boolean;
}

export default function Navbar({ onToggleSidebar, isSidebarOpen = false }: NavbarProps) {
  const { data: session, status } = useSession();
  // removed unused isMenuOpen state
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 100); // Change logo after scrolling 100px
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Show loading state while session is being fetched
  if (status === "loading") {
    return (
      <div className="navbar sticky top-0 z-50 bg-base-100/95 backdrop-blur-xl shadow-xl border-b border-primary/20">
        <div className="navbar-start">
          <button
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              onToggleSidebar?.();
            }}
            className="btn btn-ghost hover:bg-primary/10 transition-all duration-300 mr-2 rounded-lg"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h8m-8 6h16" />
            </svg>
          </button>
          <Link href="/" className="btn btn-ghost hover:bg-primary/10 transition-all duration-300 p-2 rounded-lg">
            <Image src="/favicon.png" alt="COMFOR" width={40} height={40} className="w-10 h-10 object-contain" />
          </Link>
        </div>
        <div className="navbar-end">
          <ThemeSelector className="mr-2" />
          <div className="loading loading-spinner loading-sm text-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`navbar sticky top-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-base-100/95 backdrop-blur-xl shadow-xl border-b border-primary/20'
        : 'bg-gradient-to-r from-base-200 via-base-300 to-base-200 shadow-2xl backdrop-blur-lg border-b border-primary/10'
    }`}>
      <div className="navbar-start">
        {/* Sidebar Toggle Button - Available on all screen sizes */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            onToggleSidebar?.();
          }}
          className={`btn btn-ghost hover:bg-primary/10 transition-all duration-300 mr-2 ${
            isSidebarOpen ? 'bg-primary/5 text-primary' : ''
          }`}
          title={isSidebarOpen ? "Close Sidebar" : "Open Sidebar"}
        >
          <svg
            className={`w-5 h-5 transition-transform duration-300 ${
              isSidebarOpen ? 'rotate-90' : ''
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            {isSidebarOpen ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h8m-8 6h16" />
            )}
          </svg>
        </button>

        <div className="dropdown">
          <div
            tabIndex={0}
            role="button"
            className="btn btn-ghost lg:hidden hover:bg-primary/10 transition-colors duration-300"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </div>
          <ul tabIndex={0} className="menu menu-sm dropdown-content mt-3 z-[60] p-2 shadow-2xl bg-base-100 rounded-2xl w-52 border border-primary/10">
            <li><Link href="/" className="rounded-xl hover:bg-primary/10">Home</Link></li>
            <li><Link href="/posts" className="rounded-xl hover:bg-primary/10">Posts</Link></li>
            <li><Link href="/communities" className="rounded-xl hover:bg-primary/10">Communities</Link></li>
            <li><Link href="/users" className="rounded-xl hover:bg-primary/10">Members</Link></li>
            {session?.user && (
              <>
                <li><Link href="/feed" className="rounded-xl hover:bg-accent/10">Activity Feed</Link></li>
                <li><Link href="/following" className="rounded-xl hover:bg-info/10">Following</Link></li>
                <li><Link href="/followers" className="rounded-xl hover:bg-info/10">Followers</Link></li>
                <li><Link href="/posts/create" className="rounded-xl hover:bg-accent/10">Create Post</Link></li>
                <li><Link href="/notifications" className="rounded-xl hover:bg-info/10">Notifications</Link></li>
                {(session.user as { role?: string })?.role === 'ADMIN' && (
                  <li><Link href="/admin" className="rounded-xl hover:bg-error/10">Admin</Link></li>
                )}
              </>
            )}
          </ul>
        </div>
        <Link href="/" className="btn btn-ghost hover:bg-primary/10 transition-all duration-300 p-2 rounded-lg">
          <div className="relative overflow-hidden">
            <Image
              src="/images/logo.png"
              alt="COMFOR Logo"
              width={96}
              height={96}
              className={`object-contain hover:scale-105 transition-all duration-500 ${
                isScrolled
                  ? 'w-10 h-10 opacity-0 transform scale-75'
                  : 'w-24 h-24 opacity-100 transform scale-100'
              }`}
              priority
            />
            <Image
              src="/favicon.png"
              alt="COMFOR Favicon"
              width={40}
              height={40}
              className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 object-contain hover:scale-105 transition-all duration-500 ${
                isScrolled
                  ? 'w-10 h-10 opacity-100 scale-100'
                  : 'w-6 h-6 opacity-0 scale-75'
              }`}
            />
          </div>
        </Link>
      </div>
      
      <div className="navbar-center hidden lg:flex">
        <ul className="menu menu-horizontal px-1 space-x-2">
          <li>
            <Link href="/" className="rounded-full hover:bg-primary/10 transition-all duration-300 font-medium">
              Home
            </Link>
          </li>
          <li>
            <Link href="/posts" className="rounded-full hover:bg-primary/10 transition-all duration-300 font-medium">
              Posts
            </Link>
          </li>
          <li>
            <Link href="/communities" className="rounded-full hover:bg-primary/10 transition-all duration-300 font-medium">
              Communities
            </Link>
          </li>
          <li>
            <Link href="/users" className="rounded-full hover:bg-primary/10 transition-all duration-300 font-medium">
              Members
            </Link>
          </li>
          {session?.user && (
            <>
              <li>
                <Link href="/posts/create" className="rounded-full hover:bg-accent/10 transition-all duration-300 font-medium text-accent">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Create
                </Link>
              </li>
              {(session.user as { role?: string })?.role === 'ADMIN' && (
                <li>
                  <Link href="/admin" className="rounded-full hover:bg-error/10 transition-all duration-300 font-medium text-error">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Admin
                  </Link>
                </li>
              )}
            </>
          )}
        </ul>
      </div>
      
      <div className="navbar-end">
        {/* Theme Selector */}
        <ThemeSelector className="mr-2" />
        
  {session?.user ? (
          <div className="dropdown dropdown-end">
            <div tabIndex={0} role="button" className="btn btn-ghost btn-circle avatar hover:ring-2 hover:ring-primary/30 transition-all duration-300">
              <div className="w-10 rounded-full ring-2 ring-primary/20">
                {session.user.image ? (
                  <Image
                    alt="User avatar"
                    src={session.user.image}
                    width={40}
                    height={40}
                    className="rounded-full object-cover"
                  />
                ) : (
                  <div className="bg-gradient-to-br from-primary to-accent text-primary-content rounded-full w-10 h-10 flex items-center justify-center font-bold">
                    {session.user.name?.[0]?.toUpperCase() || "U"}
                  </div>
                )}
              </div>
            </div>
            <ul tabIndex={0} className="mt-3 z-[60] p-3 shadow-2xl menu menu-sm dropdown-content bg-base-100 rounded-2xl w-64 border border-primary/10">
              <li className="mb-2">
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-primary/5 to-accent/5 rounded-xl">
                  <div>
                    <div className="font-semibold text-base">{session.user.name}</div>
                    <div className="text-xs text-base-content/60">{session.user.email}</div>
                  </div>
                  {(() => {
                    const role = (session.user as { role?: string })?.role || 'USER';
                    const color = role === 'ADMIN' ? 'badge-error' : role === 'EDITOR' ? 'badge-warning' : 'badge-info';
                    return <span className={`badge badge-sm ${color}`}>{role}</span>;
                  })()}
                </div>
              </li>
              <li>
                <Link href="/profile" className="rounded-xl hover:bg-primary/10 transition-colors duration-300">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Profile
                </Link>
              </li>
              <li>
                <Link href="/notifications" className="rounded-xl hover:bg-info/10 transition-colors duration-300">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                  </svg>
                  Notifications
                </Link>
              </li>
              <li>
                <Link href="/settings" className="rounded-xl hover:bg-primary/10 transition-colors duration-300">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Settings
                </Link>
              </li>
              <div className="divider my-1"></div>
              <li>
                <button
                  onClick={() => signOut()}
                  className="rounded-xl hover:bg-error/10 text-error transition-colors duration-300"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Sign Out
                </button>
              </li>
            </ul>
          </div>
        ) : (
          <button
            onClick={() => signIn()}
            className="btn btn-primary rounded-full px-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
            </svg>
            Sign In
          </button>
        )}
      </div>
    </div>
  );
}
