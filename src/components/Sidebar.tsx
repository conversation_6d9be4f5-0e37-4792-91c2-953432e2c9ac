"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface Community {
  id: string;
  name: string;
  slug: string;
  icon: string;
  memberCount: number;
  unreadCount: number;
  isJoined: boolean;
  isPinned: boolean;
}

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

// Real communities will be fetched from API

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const { data: session, status } = useSession();
  const pathname = usePathname();
  const [communities, setCommunities] = useState<Community[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [showAllCommunities, setShowAllCommunities] = useState(false);
  const [loading, setLoading] = useState(true);

  // Fetch communities from API
  useEffect(() => {
    const fetchCommunities = async () => {
      if (!session) {
        setLoading(false);
        return;
      }

      try {
        const response = await fetch('/api/communities');
        if (response.ok) {
          const data = await response.json();
          // Transform API data to match sidebar interface
          const transformedCommunities = data.communities?.map((community: any) => ({
            id: community.id,
            name: community.name,
            slug: community.slug,
            icon: "🏘️", // Default icon, could be stored in DB
            memberCount: 0, // Would need to be calculated from CommunityMember table
            unreadCount: 0, // Would need notification system
            isJoined: false, // Would need to check membership
            isPinned: false // Would need user preferences
          })) || [];
          setCommunities(transformedCommunities);
        }
      } catch (error) {
        console.error('Error fetching communities:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCommunities();
  }, [session]);

  // Handle ESC key to close sidebar and prevent body scroll
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when sidebar is open on mobile
      if (window.innerWidth < 1024) {
        document.body.style.overflow = 'hidden';
      }
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Close sidebar when route changes on mobile
  useEffect(() => {
    if (isOpen) {
      onClose();
    }
  }, [pathname, onClose]);

  const navigationItems = [
    { href: "/", label: "Home", icon: "🏠" },
    { href: "/posts", label: "Posts", icon: "📝" },
    { href: "/communities", label: "Communities", icon: "👥" },
    { href: "/users", label: "Members", icon: "👤" },
    ...(session ? [
      { href: "/feed", label: "Activity Feed", icon: "📱" },
      { href: "/notifications", label: "Notifications", icon: "🔔" },
      { href: "/following", label: "Following", icon: "👥" },
      { href: "/followers", label: "Followers", icon: "👥" },
      { href: "/profile", label: "Profile", icon: "👤" },
      { href: "/settings", label: "Settings", icon: "⚙️" }
    ] : [])
  ];

  const pinnedCommunities = communities.filter(c => c.isPinned && c.isJoined);
  const joinedCommunities = communities.filter(c => !c.isPinned && c.isJoined);
  const filteredCommunities = communities.filter(c => 
    c.name.toLowerCase().includes(searchQuery.toLowerCase()) && !c.isJoined
  );

  const totalUnreadCount = communities.reduce((sum, c) => sum + c.unreadCount, 0);

  const formatMemberCount = (count: number) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  const isActiveLink = (href: string) => {
    if (href === "/") {
      return pathname === "/";
    }
  if (!pathname) return false;
  return pathname.startsWith(href);
  };

  const sidebarClasses = isOpen
    ? 'fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-base-100 to-base-200/50 shadow-2xl border-r border-primary/10 z-50 transform transition-all duration-300 ease-out backdrop-blur-sm translate-x-0 shadow-3xl overflow-hidden'
    : 'fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-base-100 to-base-200/50 shadow-2xl border-r border-primary/10 z-50 transform transition-all duration-300 ease-out backdrop-blur-sm -translate-x-full shadow-none overflow-hidden';

  return (
    <>
      {/* Sidebar */}
      <div className={sidebarClasses}>
        <div className="flex flex-col h-full overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-primary/10">
            <div className="flex items-center justify-between mb-4">
              <Link href="/" className="hover:bg-primary/10 rounded-lg p-2 transition-all duration-200">
                <img
                  src="/images/logo.png"
                  alt="COMFOR Logo"
                  className="w-10 h-10 object-contain hover:scale-105 transition-transform duration-200"
                />
              </Link>
              <button
                onClick={onClose}
                className="btn btn-ghost btn-sm btn-circle hover:bg-error/10 hover:text-error transition-colors duration-200"
                title="Close Sidebar"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* User Profile Summary */}
            {session?.user && (
              <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <div className="avatar">
                    <div className="w-10 h-10 rounded-full ring-2 ring-primary/20">
                      {session.user.image ? (
                        <img src={session.user.image} alt={session.user.name || "User"} />
                      ) : (
                        <div className="bg-gradient-to-br from-primary to-accent text-primary-content rounded-full w-10 h-10 flex items-center justify-center font-bold text-sm">
                          {session.user.name?.[0]?.toUpperCase() || "U"}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-semibold text-sm truncate">{session.user.name}</p>
                    <p className="text-xs text-base-content/60 truncate">{session.user.email}</p>
                  </div>
                  {totalUnreadCount > 0 && (
                    <div className="badge badge-error badge-sm">{totalUnreadCount}</div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent">
            <div className="p-4 space-y-2">
              <h3 className="text-xs font-semibold text-base-content/60 uppercase tracking-wider mb-3">
                Navigation
              </h3>
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActiveLink(item.href)
                      ? 'bg-primary text-primary-content shadow-lg'
                      : 'hover:bg-base-200/50 text-base-content/80 hover:text-base-content'
                  }`}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span className="font-medium">{item.label}</span>
                  {item.label === "Notifications" && totalUnreadCount > 0 && (
                    <span className="badge badge-error badge-xs ml-auto">{totalUnreadCount}</span>
                  )}
                </Link>
              ))}
            </div>

            {/* Pinned Communities */}
            {session && pinnedCommunities.length > 0 && (
              <div className="p-4 space-y-2">
                <h3 className="text-xs font-semibold text-base-content/60 uppercase tracking-wider mb-3 flex items-center">
                  <span>📌 Pinned</span>
                </h3>
                {pinnedCommunities.map((community) => (
                  <Link
                    key={community.id}
                    href={`/communities/${community.slug}`}
                    className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-base-200/50 transition-all duration-200 group"
                  >
                    <span className="text-lg">{community.icon}</span>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate group-hover:text-primary transition-colors">
                        {community.name}
                      </p>
                      <p className="text-xs text-base-content/60">
                        {formatMemberCount(community.memberCount)} members
                      </p>
                    </div>
                    {community.unreadCount > 0 && (
                      <span className="badge badge-primary badge-xs">{community.unreadCount}</span>
                    )}
                  </Link>
                ))}
              </div>
            )}

            {/* Joined Communities */}
            {session && joinedCommunities.length > 0 && (
              <div className="p-4 space-y-2">
                <h3 className="text-xs font-semibold text-base-content/60 uppercase tracking-wider mb-3">
                  Communities
                </h3>
                {joinedCommunities.slice(0, showAllCommunities ? undefined : 5).map((community) => (
                  <Link
                    key={community.id}
                    href={`/communities/${community.slug}`}
                    className="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-base-200/50 transition-all duration-200 group"
                  >
                    <span className="text-lg">{community.icon}</span>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate group-hover:text-primary transition-colors">
                        {community.name}
                      </p>
                      <p className="text-xs text-base-content/60">
                        {formatMemberCount(community.memberCount)} members
                      </p>
                    </div>
                    {community.unreadCount > 0 && (
                      <span className="badge badge-primary badge-xs">{community.unreadCount}</span>
                    )}
                  </Link>
                ))}
                
                {joinedCommunities.length > 5 && (
                  <button
                    onClick={() => setShowAllCommunities(!showAllCommunities)}
                    className="w-full text-left px-3 py-2 text-sm text-base-content/60 hover:text-base-content transition-colors"
                  >
                    {showAllCommunities ? "Show less" : `Show ${joinedCommunities.length - 5} more`}
                  </button>
                )}
              </div>
            )}

            {/* Community Discovery */}
            <div className="p-4 space-y-2">
              <h3 className="text-xs font-semibold text-base-content/60 uppercase tracking-wider mb-3">
                Discover
              </h3>
              
              {/* Search */}
              <div className="form-control">
                <div className="input-group input-group-sm">
                  <span className="bg-base-200">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </span>
                  <input
                    type="text"
                    placeholder="Search communities..."
                    className="input input-bordered input-sm w-full"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              {/* Search Results */}
              {searchQuery && (
                <div className="space-y-1 max-h-40 overflow-y-auto">
                  {filteredCommunities.slice(0, 5).map((community) => (
                    <Link
                      key={community.id}
                      href={`/communities/${community.slug}`}
                      className="flex items-center space-x-2 px-2 py-1 rounded hover:bg-base-200/50 transition-all duration-200 text-sm"
                    >
                      <span>{community.icon}</span>
                      <span className="flex-1 truncate">{community.name}</span>
                      <span className="text-xs text-base-content/60">
                        {formatMemberCount(community.memberCount)}
                      </span>
                    </Link>
                  ))}
                  {filteredCommunities.length === 0 && (
                    <p className="text-xs text-base-content/60 px-2 py-1">No communities found</p>
                  )}
                </div>
              )}

              {/* Quick Actions */}
              <div className="space-y-1 pt-2">
                <Link
                  href="/communities"
                  className="flex items-center space-x-2 px-2 py-1 rounded hover:bg-base-200/50 transition-all duration-200 text-sm text-base-content/80"
                >
                  <span>🔍</span>
                  <span>Browse all communities</span>
                </Link>
                <Link
                  href="/communities/create"
                  className="flex items-center space-x-2 px-2 py-1 rounded hover:bg-base-200/50 transition-all duration-200 text-sm text-base-content/80"
                >
                  <span>➕</span>
                  <span>Create community</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
