import {
  // Navigation & UI
  HomeIcon,
  Bars3Icon,
  XMarkIcon,
  EllipsisVerticalIcon,
  MagnifyingGlassIcon,
  BellIcon,
  
  // User & Profile
  UserIcon,
  UserGroupIcon,
  UserCircleIcon,
  UsersIcon,
  
  // Content & Posts
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  PencilSquareIcon,
  DocumentPlusIcon,
  
  // Actions
  PlusIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
  HeartIcon,
  ShareIcon,
  BookmarkIcon,
  
  // Admin & Settings
  CogIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  
  // Authentication
  ArrowRightOnRectangleIcon,
  ArrowLeftOnRectangleIcon,
  LockClosedIcon,
  
  // Media & Upload
  PhotoIcon,
  VideoCameraIcon,
  CloudArrowUpIcon,
  
  // Status & Feedback
  CheckCircleIcon,
  XCircleIcon,
  InformationCircleIcon,
  ExclamationCircleIcon,
  
  // Navigation arrows
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  
  // Social
  HandThumbUpIcon,
  HandThumbDownIcon,
  ChatBubbleOvalLeftIcon,
  
  // Misc
  CalendarIcon,
  ClockIcon,
  GlobeAltIcon,
  TagIcon,
  FlagIcon,
  SparklesIcon,
  BoltIcon,
} from '@heroicons/react/24/outline';

import {
  // Solid versions for active states
  HomeIcon as HomeIconSolid,
  UserIcon as UserIconSolid,
  BellIcon as BellIconSolid,
  HeartIcon as HeartIconSolid,
  BookmarkIcon as BookmarkIconSolid,
  CheckCircleIcon as CheckCircleIconSolid,
  HandThumbUpIcon as HandThumbUpIconSolid,
  ChatBubbleOvalLeftIcon as ChatBubbleOvalLeftIconSolid,
} from '@heroicons/react/24/solid';

// Icon size classes
export const iconSizes = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4', 
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8',
  '2xl': 'w-10 h-10',
} as const;

export type IconSize = keyof typeof iconSizes;

// Common icon props interface
export interface IconProps {
  size?: IconSize;
  className?: string;
  solid?: boolean;
}

// Icon component wrapper for consistent sizing
export const Icon = ({ 
  children, 
  size = 'md', 
  className = '' 
}: { 
  children: React.ReactNode; 
  size?: IconSize; 
  className?: string; 
}) => {
  return (
    <span className={`${iconSizes[size]} ${className}`}>
      {children}
    </span>
  );
};

// Semantic icon exports with consistent naming
export const Icons = {
  // Navigation
  Home: HomeIcon,
  HomeSolid: HomeIconSolid,
  Menu: Bars3Icon,
  Close: XMarkIcon,
  Search: MagnifyingGlassIcon,
  
  // User & Profile
  User: UserIcon,
  UserSolid: UserIconSolid,
  Users: UsersIcon,
  UserGroup: UserGroupIcon,
  UserCircle: UserCircleIcon,
  
  // Content
  Post: DocumentTextIcon,
  Comment: ChatBubbleLeftRightIcon,
  Edit: PencilIcon,
  Write: PencilSquareIcon,
  Create: DocumentPlusIcon,
  
  // Actions
  Add: PlusIcon,
  Delete: TrashIcon,
  View: EyeIcon,
  Like: HeartIcon,
  LikeSolid: HeartIconSolid,
  Share: ShareIcon,
  Bookmark: BookmarkIcon,
  BookmarkSolid: BookmarkIconSolid,
  
  // Admin
  Settings: Cog6ToothIcon,
  Shield: ShieldCheckIcon,
  Analytics: ChartBarIcon,
  Warning: ExclamationTriangleIcon,
  
  // Auth
  SignIn: ArrowRightOnRectangleIcon,
  SignOut: ArrowLeftOnRectangleIcon,
  Lock: LockClosedIcon,
  
  // Media
  Photo: PhotoIcon,
  Video: VideoCameraIcon,
  Upload: CloudArrowUpIcon,
  
  // Status
  Success: CheckCircleIcon,
  SuccessSolid: CheckCircleIconSolid,
  Error: XCircleIcon,
  Info: InformationCircleIcon,
  Alert: ExclamationCircleIcon,
  
  // Navigation
  ChevronLeft: ChevronLeftIcon,
  ChevronRight: ChevronRightIcon,
  ChevronUp: ChevronUpIcon,
  ChevronDown: ChevronDownIcon,
  
  // Social
  ThumbUp: HandThumbUpIcon,
  ThumbUpSolid: HandThumbUpIconSolid,
  ThumbDown: HandThumbDownIcon,
  Chat: ChatBubbleOvalLeftIcon,
  ChatSolid: ChatBubbleOvalLeftIconSolid,
  
  // Misc
  Calendar: CalendarIcon,
  Clock: ClockIcon,
  Globe: GlobeAltIcon,
  Tag: TagIcon,
  Flag: FlagIcon,
  Sparkles: SparklesIcon,
  Bolt: BoltIcon,
  More: EllipsisVerticalIcon,
  Notification: BellIcon,
  NotificationSolid: BellIconSolid,
};

// Utility function to get icon with size
export const getIcon = (
  IconComponent: React.ComponentType<any>, 
  size: IconSize = 'md', 
  className: string = ''
) => {
  return <IconComponent className={`${iconSizes[size]} ${className}`} />;
};
