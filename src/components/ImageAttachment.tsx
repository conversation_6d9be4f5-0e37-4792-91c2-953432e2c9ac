"use client";

import { useState } from "react";
import ImageUpload from "./ImageUpload";

interface ImageAttachmentProps {
  onImagesChange: (images: { file: File; preview: string; id: string }[]) => void;
  maxImages?: number;
  maxSize?: number;
  className?: string;
  placeholder?: string;
  showPreview?: boolean;
}

interface AttachedImage {
  file: File;
  preview: string;
  id: string;
}

export default function ImageAttachment({
  onImagesChange,
  maxImages = 4,
  maxSize = 10,
  className = "",
  placeholder = "Add images to your post",
  showPreview = true
}: ImageAttachmentProps) {
  const [attachedImages, setAttachedImages] = useState<AttachedImage[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const handleImageSelect = async (file: File, preview: string) => {
    if (attachedImages.length >= maxImages) {
      return;
    }

    setIsUploading(true);
    
    try {
      // Simulate upload processing
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newImage: AttachedImage = {
        file,
        preview,
        id: generateId()
      };

      const updatedImages = [...attachedImages, newImage];
      setAttachedImages(updatedImages);
      onImagesChange(updatedImages);
    } catch (error) {
      console.error('Failed to process image:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageRemove = (imageId: string) => {
    const updatedImages = attachedImages.filter(img => img.id !== imageId);
    setAttachedImages(updatedImages);
    onImagesChange(updatedImages);
  };

  const handleImageReorder = (fromIndex: number, toIndex: number) => {
    const updatedImages = [...attachedImages];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    setAttachedImages(updatedImages);
    onImagesChange(updatedImages);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      {attachedImages.length < maxImages && (
        <div className="border-2 border-dashed border-base-300 rounded-xl p-4 hover:border-primary/50 hover:bg-base-200/30 transition-all duration-300">
          <ImageUpload
            onImageSelect={handleImageSelect}
            maxSize={maxSize}
            aspectRatio={16/9}
            shape="rectangle"
            placeholder={placeholder}
            showCrop={false}
            className="border-none p-0"
          />
          
          {isUploading && (
            <div className="flex items-center justify-center py-4">
              <span className="loading loading-spinner loading-md mr-2"></span>
              <span className="text-base-content/70">Processing image...</span>
            </div>
          )}
        </div>
      )}

      {/* Image Previews */}
      {showPreview && attachedImages.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-base-content/80">
              Attached Images ({attachedImages.length}/{maxImages})
            </h4>
            <button
              type="button"
              onClick={() => {
                setAttachedImages([]);
                onImagesChange([]);
              }}
              className="btn btn-ghost btn-xs text-error hover:bg-error/10"
            >
              Remove All
            </button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {attachedImages.map((image, index) => (
              <div
                key={image.id}
                className="relative group bg-base-200 rounded-xl overflow-hidden"
              >
                {/* Image */}
                <div className="aspect-video relative">
                  <img
                    src={image.preview}
                    alt={`Attachment ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
                    {/* Move Left */}
                    {index > 0 && (
                      <button
                        type="button"
                        onClick={() => handleImageReorder(index, index - 1)}
                        className="btn btn-sm btn-circle btn-ghost text-white hover:bg-white/20"
                        title="Move left"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>
                    )}

                    {/* Remove */}
                    <button
                      type="button"
                      onClick={() => handleImageRemove(image.id)}
                      className="btn btn-sm btn-circle btn-error text-white"
                      title="Remove image"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>

                    {/* Move Right */}
                    {index < attachedImages.length - 1 && (
                      <button
                        type="button"
                        onClick={() => handleImageReorder(index, index + 1)}
                        className="btn btn-sm btn-circle btn-ghost text-white hover:bg-white/20"
                        title="Move right"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>

                {/* Image Info */}
                <div className="p-3">
                  <div className="flex items-center justify-between text-xs text-base-content/60">
                    <span className="truncate flex-1 mr-2">{image.file.name}</span>
                    <span>{(image.file.size / 1024 / 1024).toFixed(1)}MB</span>
                  </div>
                </div>

                {/* Order Badge */}
                <div className="absolute top-2 left-2 badge badge-primary badge-sm">
                  {index + 1}
                </div>
              </div>
            ))}
          </div>

          {/* Upload Limits Info */}
          <div className="text-xs text-base-content/50 flex items-center space-x-4">
            <span>• Max {maxImages} images</span>
            <span>• Up to {maxSize}MB each</span>
            <span>• JPG, PNG, GIF, WebP supported</span>
          </div>
        </div>
      )}

      {/* Max Images Reached */}
      {attachedImages.length >= maxImages && (
        <div className="alert alert-warning">
          <svg className="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          <span>Maximum number of images reached ({maxImages}). Remove some images to add more.</span>
        </div>
      )}
    </div>
  );
}
