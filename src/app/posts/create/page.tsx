"use client";

import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { renderMarkdown } from '@/lib/markdown';

// Types
interface Community {
  id: string;
  name: string;
  slug: string;
  image: string | null;
  isPrivate: boolean;
}

interface ImageFile {
  file: File;
  preview: string;
  id: string;
}

export default function CreatePostPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [communities, setCommunities] = useState<Community[]>([]);
  const [loadingCommunities, setLoadingCommunities] = useState(true);
  const [images, setImages] = useState<ImageFile[]>([]);
  const [dragActive, setDragActive] = useState(false);

  const [formData, setFormData] = useState({
    title: "",
    content: "",
    published: true,
    communityId: "",
    videoUrl: "",
  });

  // Load communities and handle pre-selection
  useEffect(() => {
    const fetchCommunities = async () => {
      try {
        const response = await fetch('/api/communities?limit=100');
        if (response.ok) {
          const data = await response.json();
          setCommunities(data.communities || []);

          // Pre-select community from query parameter
          const communitySlug = searchParams?.get('community');
          if (communitySlug) {
            const community = data.communities.find((c: Community) => c.slug === communitySlug);
            if (community) {
              setFormData(prev => ({ ...prev, communityId: community.id }));
            }
          }
        }
      } catch (error) {
        console.error('Error fetching communities:', error);
      } finally {
        setLoadingCommunities(false);
      }
    };

    if (session?.user) {
      fetchCommunities();
    }
  }, [session, searchParams]);

  // Helper functions
  const generateId = () => Math.random().toString(36).substr(2, 9);

  const isValidImageType = (file: File) => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    return validTypes.includes(file.type);
  };

  const isValidVideoUrl = (url: string) => {
    if (!url) return true; // Empty is valid
    const videoPatterns = [
      /^https?:\/\/(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)/,
      /^https?:\/\/(www\.)?vimeo\.com\/\d+/,
      /^https?:\/\/(www\.)?dailymotion\.com\/video\//,
      /^https?:\/\/(www\.)?twitch\.tv\/videos\//,
    ];
    return videoPatterns.some(pattern => pattern.test(url));
  };

  const getVideoEmbedUrl = (url: string) => {
    if (!url) return null;

    // YouTube
    const youtubeMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
    if (youtubeMatch) {
      return `https://www.youtube.com/embed/${youtubeMatch[1]}`;
    }

    // Vimeo
    const vimeoMatch = url.match(/vimeo\.com\/(\d+)/);
    if (vimeoMatch) {
      return `https://player.vimeo.com/video/${vimeoMatch[1]}`;
    }

    return null;
  };

  // Redirect if not authenticated
  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300 flex items-center justify-center">
        <div className="card bg-base-100 shadow-xl border border-base-300">
          <div className="card-body text-center py-16">
            <div className="loading loading-spinner loading-lg text-primary"></div>
            <p className="text-base-content/60 mt-4">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!session?.user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300 flex items-center justify-center">
        <div className="card bg-base-100 shadow-xl border border-error/20 max-w-md">
          <div className="card-body text-center py-16">
            <div className="text-6xl mb-4">🔒</div>
            <h1 className="text-2xl font-bold mb-4">Authentication Required</h1>
            <p className="text-base-content/60 mb-6">You need to be signed in to create a post.</p>
            <Link href="/auth/signin" className="btn btn-primary">
              Sign In
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Image handling functions
  const handleImageUpload = (files: FileList | null) => {
    if (!files) return;

    const newImages: ImageFile[] = [];
    Array.from(files).forEach(file => {
      if (isValidImageType(file) && images.length + newImages.length < 5) {
        const preview = URL.createObjectURL(file);
        newImages.push({
          file,
          preview,
          id: generateId(),
        });
      }
    });

    setImages(prev => [...prev, ...newImages]);
  };

  const removeImage = (id: string) => {
    setImages(prev => {
      const imageToRemove = prev.find(img => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return prev.filter(img => img.id !== id);
    });
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageUpload(e.dataTransfer.files);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.title.trim() || !formData.content.trim()) {
      alert('Please fill in both title and content');
      return;
    }

    if (formData.videoUrl && !isValidVideoUrl(formData.videoUrl)) {
      alert('Please enter a valid video URL from YouTube, Vimeo, or other supported platforms');
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload images first if any
      const imageUrls: string[] = [];
      if (images.length > 0) {
        for (const image of images) {
          const formData = new FormData();
          formData.append('file', image.file);

          const uploadResponse = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          });

          if (uploadResponse.ok) {
            const { url } = await uploadResponse.json();
            imageUrls.push(url);
          }
        }
      }

      // Create the post
      const postData = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        published: formData.published,
        communityId: formData.communityId || null,
        videoUrl: formData.videoUrl && formData.videoUrl.trim() ? formData.videoUrl.trim() : null,
        images: imageUrls,
      };

      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to create post (${response.status})`);
      }

      const newPost = await response.json();

      // Clean up image previews
      images.forEach(image => URL.revokeObjectURL(image.preview));

      // Redirect to the new post
      router.push(`/posts/${newPost.id}`);
    } catch (error) {
      console.error("Error creating post:", error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create post. Please try again.';
      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value
    }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
      <div className="page-container">
        {/* Header */}
        <div className="flex items-center justify-between page-header">
          <Link href="/posts" className="btn btn-ghost rounded-full hover:bg-primary/10 transition-all duration-300 group">
            <svg className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
            Back to Posts
          </Link>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="text-center page-header">
            <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-4">
              Create New Post
            </h1>
            <p className="text-xl text-base-content/70">Share your thoughts, images, and videos with the community</p>
          </div>

          {/* Form */}
          <div className="enhanced-card">
            <div className="enhanced-card-body">
              <form onSubmit={handleSubmit} className="form-section">
                {/* Community Selection */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg">Community</span>
                    <span className="label-text-alt text-base-content/60">Optional</span>
                  </label>
                  <select
                    name="communityId"
                    value={formData.communityId}
                    onChange={handleChange}
                    className="select select-bordered select-primary w-full bg-base-100/50"
                    disabled={loadingCommunities}
                  >
                    <option value="">Select a community (optional)</option>
                    {communities.map((community) => (
                      <option key={community.id} value={community.id}>
                        {community.name} {community.isPrivate ? '🔒' : ''}
                      </option>
                    ))}
                  </select>
                  {loadingCommunities && (
                    <label className="label">
                      <span className="label-text-alt">
                        <div className="loading loading-spinner loading-xs mr-2"></div>
                        Loading communities...
                      </span>
                    </label>
                  )}
                </div>

                {/* Title */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg">Post Title</span>
                    <span className="label-text-alt text-error">*</span>
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    placeholder="Enter a compelling title for your post"
                    className="input input-bordered input-primary w-full bg-base-100/50 text-lg"
                    required
                    maxLength={200}
                  />
                  <label className="label">
                    <span className="label-text-alt text-base-content/50">
                      {formData.title.length}/200 characters
                    </span>
                  </label>
                </div>

                {/* Content */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg flex items-center gap-2">
                      Content
                      <span className="relative group inline-block">
                        <button
                          type="button"
                          tabIndex={0}
                          aria-label="Markdown help"
                          className="w-5 h-5 flex items-center justify-center rounded-full bg-base-300 text-base-content/70 text-xs font-bold hover:bg-primary hover:text-primary-content transition-colors"
                        >i</button>
                        {/* Tooltip Panel */}
                        <div className="invisible opacity-0 group-hover:visible group-hover:opacity-100 group-focus-within:visible group-focus-within:opacity-100 transition-opacity duration-200 absolute left-0 top-6 z-30 w-72 p-4 rounded-xl shadow-2xl border border-base-300 bg-base-100 text-xs leading-relaxed space-y-2">
                          <p className="font-semibold text-sm">Markdown Quick Guide</p>
                          <ul className="space-y-1 list-disc list-inside">
                            <li><code className="kbd kbd-xs">#</code> Heading 1 (<code className="kbd kbd-xs">##</code> H2 ...)</li>
                            <li><code>**bold**</code> <code>*italic*</code> <code>~~strike~~</code></li>
                            <li>Lists: <code>- item</code> or numbered <code>1. item</code></li>
                            <li>Link: <code>[text](https://url)</code></li>
                            <li>Image: <code>![alt](https://url)</code></li>
                            <li>Inline code: <code>`code`</code></li>
                            <li>Block code:
                              <pre className="mt-1 bg-base-200/70 p-1 rounded"><code>```js
console.log(&#39;hi&#39;)
```</code></pre>
                            </li>
                            <li>Quote: <code>&gt; quoted text</code></li>
                          </ul>
                          <p className="text-[10px] text-base-content/60">Your preview below currently shows raw text; published posts may render markdown where implemented.</p>
                        </div>
                      </span>
                    </span>
                    <span className="label-text-alt text-error">*</span>
                  </label>
                  <textarea
                    name="content"
                    value={formData.content}
                    onChange={handleChange}
                    placeholder="Write your post content here. You can use markdown formatting."
                    className="textarea textarea-bordered textarea-primary h-64 resize-none bg-base-100/50 text-base"
                    required
                  />
                  <label className="label">
                    <span className="label-text-alt text-base-content/50">
                      Markdown formatting supported
                    </span>
                  </label>
                </div>

                {/* Image Upload */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg">Images</span>
                    <span className="label-text-alt text-base-content/60">Up to 5 images</span>
                  </label>

                  {/* Drag and Drop Area */}
                  <div
                    className={`border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 ${
                      dragActive
                        ? 'border-primary bg-primary/10'
                        : 'border-base-300 hover:border-primary/50 hover:bg-base-200/50'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <div className="space-y-4">
                      <div className="text-4xl">📸</div>
                      <div>
                        <p className="text-lg font-medium mb-2">Drag and drop images here</p>
                        <p className="text-base-content/60 mb-4">or</p>
                        <button
                          type="button"
                          onClick={() => fileInputRef.current?.click()}
                          className="btn btn-outline btn-primary"
                        >
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                          </svg>
                          Choose Images
                        </button>
                      </div>
                      <p className="text-sm text-base-content/50">
                        Supports JPG, PNG, GIF, WebP (max 5 images)
                      </p>
                    </div>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                    className="hidden"
                  />

                  {/* Image Previews */}
                  {images.length > 0 && (
                    <div className="mt-6">
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                        {images.map((image) => (
                          <div key={image.id} className="relative group">
                            <img
                              src={image.preview}
                              alt="Preview"
                              className="w-full h-24 object-cover rounded-lg border border-base-300"
                            />
                            <button
                              type="button"
                              onClick={() => removeImage(image.id)}
                              className="absolute -top-2 -right-2 btn btn-circle btn-error btn-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Video URL */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg">Video URL</span>
                    <span className="label-text-alt text-base-content/60">Optional</span>
                  </label>
                  <input
                    type="url"
                    name="videoUrl"
                    value={formData.videoUrl}
                    onChange={handleChange}
                    placeholder="Paste YouTube, Vimeo, or other video URL here"
                    className="input input-bordered input-primary w-full bg-base-100/50"
                  />
                  <label className="label">
                    <span className="label-text-alt text-base-content/50">
                      Supports YouTube, Vimeo, Dailymotion, and Twitch
                    </span>
                  </label>

                  {/* Video Preview */}
                  {formData.videoUrl && isValidVideoUrl(formData.videoUrl) && (
                    <div className="mt-4">
                      <div className="aspect-video bg-base-200 rounded-lg overflow-hidden">
                        <iframe
                          src={getVideoEmbedUrl(formData.videoUrl) || ''}
                          className="w-full h-full"
                          frameBorder="0"
                          allowFullScreen
                          title="Video preview"
                        />
                      </div>
                    </div>
                  )}

                  {formData.videoUrl && !isValidVideoUrl(formData.videoUrl) && (
                    <div className="alert alert-warning mt-4">
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <span>Please enter a valid video URL from a supported platform</span>
                    </div>
                  )}
                </div>

                {/* Publishing Options */}
                <div className="form-control">
                  <label className="label cursor-pointer justify-start space-x-3">
                    <input
                      type="checkbox"
                      name="published"
                      checked={formData.published}
                      onChange={handleChange}
                      className="checkbox checkbox-primary checkbox-lg"
                    />
                    <div>
                      <span className="label-text font-semibold text-lg">Publish immediately</span>
                      <div className="text-sm text-base-content/70">
                        Uncheck to save as draft for later
                      </div>
                    </div>
                  </label>
                </div>

                {/* Preview Section */}
                <div className="divider">
                  <span className="text-lg font-semibold">Preview</span>
                </div>
                <div className="card bg-gradient-to-br from-base-200 to-base-300/50 shadow-lg border border-base-300">
                  <div className="card-body">
                    {/* Community Badge */}
                    {formData.communityId && (
                      <div className="mb-4">
                        <div className="badge badge-primary badge-lg">
                          {communities.find(c => c.id === formData.communityId)?.name || 'Community'}
                        </div>
                      </div>
                    )}

                    <h3 className="card-title text-2xl mb-4">
                      {formData.title || "Your post title will appear here"}
                    </h3>

                    <div className="prose max-w-none mb-6">
                      {formData.content ? (
                        <div
                          className="text-base leading-relaxed"
                          dangerouslySetInnerHTML={{ __html: renderMarkdown(formData.content) }}
                        />
                      ) : (
                        <p className="text-base-content/50 italic">
                          Your post content will appear here as you type
                        </p>
                      )}
                    </div>

                    {/* Image Previews */}
                    {images.length > 0 && (
                      <div className="mb-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {images.map((image) => (
                            <img
                              key={image.id}
                              src={image.preview}
                              alt="Post image"
                              className="w-full h-48 object-cover rounded-lg"
                            />
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Video Preview */}
                    {formData.videoUrl && isValidVideoUrl(formData.videoUrl) && (
                      <div className="mb-6">
                        <div className="aspect-video bg-base-300 rounded-lg overflow-hidden">
                          <iframe
                            src={getVideoEmbedUrl(formData.videoUrl) || ''}
                            className="w-full h-full"
                            frameBorder="0"
                            allowFullScreen
                            title="Video preview"
                          />
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t border-base-300">
                      <div className="flex items-center space-x-3">
                        <div className="avatar">
                          <div className="w-10 h-10 rounded-full">
                            <img
                              src={session.user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session.user.name || 'User')}&background=random`}
                              alt={session.user.name || 'User'}
                              className="rounded-full"
                            />
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{session.user.name}</span>
                            <span className="badge badge-info badge-sm">
                              {(session.user as any)?.role || "USER"}
                            </span>
                          </div>
                          <span className="text-sm text-base-content/60">Just now</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-6">
                        <div className="flex items-center space-x-2 text-error">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                          </svg>
                          <span>0</span>
                        </div>
                        <div className="flex items-center space-x-2 text-info">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          <span>0</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-end pt-6 border-t border-base-300">
                  <Link href="/posts" className="btn btn-outline btn-lg rounded-full px-8">
                    Cancel
                  </Link>
                  <button
                    type="submit"
                    disabled={isSubmitting || !formData.title.trim() || !formData.content.trim()}
                    className="btn btn-primary btn-lg rounded-full px-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    {isSubmitting ? (
                      <>
                        <span className="loading loading-spinner loading-sm"></span>
                        {formData.published ? "Publishing..." : "Saving Draft..."}
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                        {formData.published ? "Publish Post" : "Save Draft"}
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
