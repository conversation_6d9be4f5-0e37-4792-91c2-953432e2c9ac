"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { getRouteParam } from '@/lib/params';

// Types
interface Community {
  id: string;
  name: string;
  description: string;
  slug: string;
  image: string | null;
  isPrivate: boolean;
  createdAt: string;
  updatedAt: string;
  creatorId: string;
  creator: {
    id: string;
    name: string;
    email: string;
    role: string;
    image: string;
  };
  posts: Post[];
  members: Member[];
  _count: {
    posts: number;
    members: number;
  };
  isMember: boolean;
  userRole: string | null;
}

interface Post {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  published: boolean;
  createdAt: string;
  author: {
    id: string;
    name: string;
    email: string;
    role: string;
    image: string;
  };
  _count: {
    comments: number;
    likes: number;
  };
  // Optional media & taxonomy fields (may not always be returned by API)
  images?: string[];
  videoUrl?: string | null;
  tags?: string[]; // Not yet persisted in schema; shown when provided by API/mock
}

interface Member {
  id: string;
  role: string;
  joinedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    image: string;
  };
}

export default function CommunityDetailPage() {
  const { data: session } = useSession();
  const params = useParams();
  // Null-safe param extraction
  const slug = getRouteParam(params, 'slug');
  const [community, setCommunity] = useState<Community | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"posts" | "members" | "about">("posts");
  const [joinLoading, setJoinLoading] = useState(false);

  // Fetch community data (memoized to satisfy react-hooks/exhaustive-deps)
  const fetchCommunity = useCallback(async () => {
    if (!slug) return; // Guard against empty slug during initial render
    const controller = new AbortController();
    try {
      setLoading(true);
      const response = await fetch(`/api/communities/${slug}`, { signal: controller.signal });
      if (!response.ok) {
        if (response.status === 404) throw new Error('Community not found');
        throw new Error('Failed to fetch community');
      }
      const data: Community = await response.json();
      setCommunity(data);
      setError(null);
    } catch (err) {
      if (err instanceof DOMException && err.name === 'AbortError') return; // Ignore aborted fetch
      setError(err instanceof Error ? err.message : 'Failed to fetch community');
    } finally {
      setLoading(false);
    }
    return () => controller.abort();
  }, [slug]);

  // Handle join/leave community
  const handleJoinLeave = async () => {
    if (!session || !community) {
      alert('Please sign in to join communities');
      return;
    }

    try {
      setJoinLoading(true);
      const method = community.isMember ? 'DELETE' : 'POST';
      const response = await fetch(`/api/communities/${slug}/members`, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update membership');
      }

      // Refresh community data
      await fetchCommunity();
    } catch (error) {
      console.error('Error updating membership:', error);
      alert(error instanceof Error ? error.message : 'Failed to update membership');
    } finally {
      setJoinLoading(false);
    }
  };

  // Load community on component mount
  useEffect(() => {
    fetchCommunity();
  }, [fetchCommunity]);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "Less than an hour ago";
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "ADMIN": return "badge-error";
      case "EDITOR": return "badge-warning";
      default: return "badge-info";
    }
  };

  const getMemberRoleBadgeColor = (role: string) => {
    switch (role) {
      case "MODERATOR": return "badge-warning";
      case "MEMBER": return "badge-info";
      default: return "badge-ghost";
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
        <div className="container mx-auto px-4 py-8">
          <div className="card bg-base-100 shadow-xl border border-base-300">
            <div className="card-body text-center py-16">
              <div className="loading loading-spinner loading-lg text-primary"></div>
              <p className="text-base-content/60 mt-4">Loading community...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !community) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
        <div className="container mx-auto px-4 py-8">
          <div className="card bg-base-100 shadow-xl border border-error/20">
            <div className="card-body text-center py-16">
              <div className="text-6xl mb-4">🏘️</div>
              <h3 className="text-2xl font-bold mb-2 text-error">Community Not Found</h3>
              <p className="text-base-content/60 mb-6">{error || 'The community you are looking for does not exist.'}</p>
              <Link href="/communities" className="btn btn-primary">
                Back to Communities
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
      <div className="page-container">
        {/* Navigation */}
        <div className="flex items-center justify-between page-header">
          <Link href="/communities" className="btn btn-ghost rounded-full hover:bg-primary/10 transition-all duration-300 group">
            <svg className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
            </svg>
            Back to Communities
          </Link>
        </div>

        {/* Community Header */}
        <div className="enhanced-card section-spacing">
          <div className="enhanced-card-body">
            <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8">
              {/* Community Image */}
              <div className="avatar">
                <div className="w-32 h-32 rounded-2xl ring-4 ring-primary/20 ring-offset-4 ring-offset-base-100">
                  {community.image ? (
                    <Image
                      src={community.image}
                      alt={community.name}
                      width={128}
                      height={128}
                      className="rounded-2xl object-cover w-32 h-32"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center rounded-2xl">
                      <div className="text-4xl">🏘️</div>
                    </div>
                  )}
                </div>
              </div>

              {/* Community Info */}
              <div className="flex-1 text-center lg:text-left">
                <div className="flex flex-col lg:flex-row lg:items-center gap-4 mb-4">
                  <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
                    {community.name}
                  </h1>
                  <div className="flex items-center justify-center lg:justify-start gap-2">
                    {community.isPrivate && (
                      <span className="badge badge-warning badge-lg">Private</span>
                    )}
                    {community.isMember && (
                      <span className="badge badge-success badge-lg">Member</span>
                    )}
                  </div>
                </div>

                <p className="text-lg text-base-content/70 mb-6 max-w-2xl">
                  {community.description}
                </p>

                {/* Stats */}
                <div className="stats shadow-lg bg-gradient-to-r from-primary/5 to-accent/5 mb-6">
                  <div className="stat">
                    <div className="stat-title text-xs">Members</div>
                    <div className="stat-value text-2xl text-primary">{community._count.members}</div>
                  </div>
                  <div className="stat">
                    <div className="stat-title text-xs">Posts</div>
                    <div className="stat-value text-2xl text-accent">{community._count.posts}</div>
                  </div>
                </div>

                {/* Creator Info */}
                <div className="flex items-center justify-center lg:justify-start space-x-3 mb-6">
                  <div className="avatar">
                    <div className="w-8 h-8 rounded-full">
                      <Image
                        src={community.creator.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(community.creator.name)}&background=random`}
                        alt={community.creator.name}
                        width={32}
                        height={32}
                        className="rounded-full object-cover w-8 h-8"
                      />
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-sm">Created by {community.creator.name}</span>
                      <span className={`badge ${getRoleBadgeColor(community.creator.role)} badge-xs`}>
                        {community.creator.role}
                      </span>
                    </div>
                    <div className="text-xs text-base-content/60">
                      {formatTimeAgo(community.createdAt)}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                {session && (
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button
                      onClick={handleJoinLeave}
                      disabled={joinLoading}
                      className={`btn btn-lg rounded-full px-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 ${
                        community.isMember 
                          ? 'btn-outline btn-error' 
                          : 'btn-primary'
                      }`}
                    >
                      {joinLoading ? (
                        <div className="loading loading-spinner loading-sm"></div>
                      ) : (
                        <>
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={
                              community.isMember 
                                ? "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                                : "M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
                            } />
                          </svg>
                          {community.isMember ? 'Leave Community' : 'Join Community'}
                        </>
                      )}
                    </button>
                    
                    {community.isMember && (
                      <Link href={`/posts/create?community=${community.slug}`} className="btn btn-outline btn-lg rounded-full px-8 hover:btn-primary transition-all duration-300">
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                        </svg>
                        Create Post
                      </Link>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="tabs tabs-boxed bg-base-100/80 backdrop-blur-sm shadow-xl border border-primary/10 mb-8">
          <button
            className={`tab tab-lg ${activeTab === "posts" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("posts")}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Posts ({community._count.posts})
          </button>
          <button
            className={`tab tab-lg ${activeTab === "members" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("members")}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Members ({community._count.members})
          </button>
          <button
            className={`tab tab-lg ${activeTab === "about" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("about")}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            About
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === "posts" ? (
          <div className="grid grid-cols-2 gap-6 justify-items-center">
            {community.posts && community.posts.length > 0 ? (
              community.posts.map((post) => (
                <div key={post.id} className="card h-full w-full max-w-[500px] bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl hover:shadow-2xl transition-all duration-300 border border-primary/5 hover:border-primary/20 flex flex-col">
                  <div className="card-body flex flex-col">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="avatar">
                          <div className="w-10 h-10 rounded-full">
                            <Image
                              src={post.author.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(post.author.name)}&background=random`}
                              alt={post.author.name}
                              width={40}
                              height={40}
                              className="rounded-full object-cover w-10 h-10"
                            />
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-sm">{post.author.name}</span>
                            <span className={`badge ${getRoleBadgeColor(post.author.role)} badge-xs`}>
                              {post.author.role}
                            </span>
                          </div>
                          <div className="text-xs text-base-content/60">
                            {formatTimeAgo(post.createdAt)}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex-1">
                      <h3 className="card-title text-xl mb-2 hover:text-primary transition-colors">
                        <Link href={`/posts/${post.id}`} className="hover:underline">
                          {post.title}
                        </Link>
                      </h3>
                      <p className="text-base-content/70 line-clamp-3 mb-4">
                        {post.excerpt}
                      </p>
                      {/* Media Indicators */}
                      {(post.images?.length || post.videoUrl) && (
                        <div className="flex items-center space-x-3 text-xs mb-3">
                          {post.images && post.images.length > 0 && (
                            <span className="flex items-center space-x-1 text-accent">
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              <span>{post.images.length}</span>
                            </span>
                          )}
                          {post.videoUrl && (
                            <span className="flex items-center space-x-1 text-secondary">
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                              <span className="sr-only">Video</span>
                            </span>
                          )}
                        </div>
                      )}
                      {/* Tags */}
                      {Array.isArray(post.tags) && post.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mb-4">
                          {post.tags.slice(0, 6).map((tag) => (
                            <span key={tag} className="badge badge-outline badge-sm">
                              {tag}
                            </span>
                          ))}
                          {post.tags.length > 6 && (
                            <span className="badge badge-ghost badge-sm">+{post.tags.length - 6}</span>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-between mt-auto pt-4">
                      <div className="flex items-center space-x-4 text-sm text-base-content/60">
                        <span className="flex items-center space-x-1">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                          <span>{post._count.likes}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          <span>{post._count.comments}</span>
                        </span>
                      </div>

                      <Link href={`/posts/${post.id}`} className="btn btn-primary btn-sm">
                        Read More
                      </Link>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="card bg-base-100 shadow-xl border border-base-300 col-span-2 w-full max-w-[500px] justify-self-center">
                <div className="card-body text-center py-16">
                  <div className="text-6xl mb-4">📝</div>
                  <h3 className="text-2xl font-bold mb-2">No Posts Yet</h3>
                  <p className="text-base-content/60 mb-6">
                    Be the first to start a discussion in this community!
                  </p>
                  {community.isMember && session && (
                    <Link href={`/posts/create?community=${community.slug}`} className="btn btn-primary">
                      Create First Post
                    </Link>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : activeTab === "members" ? (
          <div className="card bg-base-100 shadow-xl border border-base-300">
            <div className="card-body">
              <h3 className="text-2xl font-bold mb-6">Community Members</h3>
              {community.members && community.members.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {community.members.map((member) => (
                    <div key={member.id} className="flex items-center space-x-3 p-4 bg-base-200/50 rounded-xl hover:bg-base-200/80 transition-colors duration-300">
                      <div className="avatar">
                        <div className="w-12 h-12 rounded-full">
                          <Image
                            src={member.user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(member.user.name)}&background=random`}
                            alt={member.user.name}
                            width={48}
                            height={48}
                            className="rounded-full object-cover w-12 h-12"
                          />
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Link href={`/profile/${member.user.id}`} className="font-medium hover:text-primary transition-colors">
                            {member.user.name}
                          </Link>
                          <span className={`badge ${getRoleBadgeColor(member.user.role)} badge-xs`}>
                            {member.user.role}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`badge ${getMemberRoleBadgeColor(member.role)} badge-xs`}>
                            {member.role}
                          </span>
                          <span className="text-xs text-base-content/60">
                            Joined {formatTimeAgo(member.joinedAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">👥</div>
                  <h3 className="text-xl font-bold mb-2">No Members Yet</h3>
                  <p className="text-base-content/60">Be the first to join this community!</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="card bg-base-100 shadow-xl border border-base-300">
            <div className="card-body">
              <h3 className="text-2xl font-bold mb-6">About {community.name}</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-semibold mb-2">Description</h4>
                  <p className="text-base-content/70">{community.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2">Community Stats</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Total Members:</span>
                        <span className="font-medium">{community._count.members}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Posts:</span>
                        <span className="font-medium">{community._count.posts}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Created:</span>
                        <span className="font-medium">{formatTimeAgo(community.createdAt)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Privacy:</span>
                        <span className={`badge ${community.isPrivate ? 'badge-warning' : 'badge-success'} badge-sm`}>
                          {community.isPrivate ? 'Private' : 'Public'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Community Creator</h4>
                    <div className="flex items-center space-x-3 p-4 bg-base-200/50 rounded-xl">
                      <div className="avatar">
                        <div className="w-12 h-12 rounded-full">
                          <Image
                            src={community.creator.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(community.creator.name)}&background=random`}
                            alt={community.creator.name}
                            width={48}
                            height={48}
                            className="rounded-full object-cover w-12 h-12"
                          />
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <Link href={`/profile/${community.creator.id}`} className="font-medium hover:text-primary transition-colors">
                            {community.creator.name}
                          </Link>
                          <span className={`badge ${getRoleBadgeColor(community.creator.role)} badge-xs`}>
                            {community.creator.role}
                          </span>
                        </div>
                        <div className="text-xs text-base-content/60">
                          Community Founder
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
