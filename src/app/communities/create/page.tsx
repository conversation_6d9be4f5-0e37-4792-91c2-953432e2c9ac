"use client";

import { useSession } from "next-auth/react";
import { useState, useRef } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";

// Types
interface ImageFile {
  file: File;
  preview: string;
  id: string;
}

export default function CreateCommunityPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [communityImage, setCommunityImage] = useState<ImageFile | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    slug: "",
    isPrivate: false,
    tags: "",
  });

  // Helper functions
  const generateId = () => Math.random().toString(36).substr(2, 9);

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
      .trim() || 'community'; // Fallback if empty
  };

  const isValidImageType = (file: File) => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    return validTypes.includes(file.type);
  };

  // Image handling functions
  const handleImageUpload = (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0]; // Only take the first file for community image
    if (isValidImageType(file)) {
      // Clean up previous image preview
      if (communityImage) {
        URL.revokeObjectURL(communityImage.preview);
      }

      const preview = URL.createObjectURL(file);
      setCommunityImage({
        file,
        preview,
        id: generateId(),
      });
    }
  };

  const removeImage = () => {
    if (communityImage) {
      URL.revokeObjectURL(communityImage.preview);
      setCommunityImage(null);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageUpload(e.dataTransfer.files);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!session) {
      router.push("/auth/signin");
      return;
    }

    // Validation
    if (!formData.name.trim() || !formData.description.trim()) {
      alert('Please fill in both name and description');
      return;
    }

    // Validate name length
    if (formData.name.trim().length > 100) {
      alert('Community name must be 100 characters or less');
      return;
    }

    // Validate description length
    if (formData.description.trim().length > 1000) {
      alert('Description must be 1000 characters or less');
      return;
    }

    setIsSubmitting(true);

    try {
      let imageUrl = null;

      // Upload image first if provided
      if (communityImage) {
        const imageFormData = new FormData();
        imageFormData.append('file', communityImage.file);

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: imageFormData,
        });

        if (uploadResponse.ok) {
          const { url } = await uploadResponse.json();
          imageUrl = url && url.trim() ? url.trim() : null;
        } else {
          console.error('Image upload failed:', uploadResponse.status);
        }
      }

      // Generate slug from name if not provided
      const slug = formData.slug.trim() || generateSlug(formData.name);

      // Validate slug
      if (!slug || slug.length > 50 || !/^[a-z0-9-]+$/.test(slug)) {
        alert('Invalid community URL. Please use only lowercase letters, numbers, and hyphens.');
        return;
      }

      // Create the community
      const communityData: any = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        slug: slug,
        isPrivate: formData.isPrivate,
      };

      // Only include image if we have a valid URL
      if (imageUrl && imageUrl.trim() && (imageUrl.startsWith('http') || imageUrl.startsWith('/'))) {
        communityData.image = imageUrl;
      }

      console.log('Sending community data:', communityData);

      const response = await fetch('/api/communities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(communityData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        if (errorData.details && Array.isArray(errorData.details)) {
          // Show specific validation errors
          const validationErrors = errorData.details.map((detail: any) =>
            `${detail.path?.join('.')}: ${detail.message}`
          ).join('\n');
          throw new Error(`Validation failed:\n${validationErrors}`);
        }
        throw new Error(errorData.error || `Failed to create community (${response.status})`);
      }

      const newCommunity = await response.json();

      // Clean up image preview
      if (communityImage) {
        URL.revokeObjectURL(communityImage.preview);
      }

      // Redirect to the new community
      router.push(`/communities/${newCommunity.slug}`);
    } catch (error) {
      console.error("Error creating community:", error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create community. Please try again.';
      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'radio' && name === 'isPrivate') {
      // Handle radio buttons for privacy setting
      const booleanValue = value === 'true';
      setFormData(prev => ({ ...prev, [name]: booleanValue }));
    } else {
      // Auto-generate slug when name changes (unless user manually edited slug)
      if (name === 'name') {
        const autoSlug = generateSlug(value);
        setFormData(prev => ({
          ...prev,
          [name]: value,
          // Only update slug if it's empty or matches the previous auto-generated slug
          slug: !prev.slug || prev.slug === generateSlug(prev.name) ? autoSlug : prev.slug
        }));
      } else {
        setFormData(prev => ({ ...prev, [name]: value }));
      }
    }
  };

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30 flex items-center justify-center">
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10 w-full max-w-md">
          <div className="card-body text-center py-12">
            <div className="text-6xl mb-4">🔒</div>
            <h2 className="text-2xl font-bold mb-2">Authentication Required</h2>
            <p className="text-base-content/70 mb-6">
              Please sign in to create a new community
            </p>
            <Link href="/auth/signin" className="btn btn-primary btn-lg rounded-full px-8">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              Sign In
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30">
      <div className="max-w-4xl mx-auto content-spacing page-container">
        {/* Header */}
        <div className="enhanced-card">
          <div className="enhanced-card-body">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-2">
                  Create Community
                </h1>
                <p className="text-base-content/70 text-lg">
                  Build a space for like-minded people to connect and share
                </p>
              </div>
              <Link href="/communities" className="btn btn-ghost rounded-full">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Communities
              </Link>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="form-section">
          {/* Basic Information */}
          <div className="enhanced-card">
            <div className="enhanced-card-body">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-6">
                Basic Information
              </h2>
              
              <div className="space-y-6">
                {/* Community Name */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg">Community Name</span>
                    <span className="label-text-alt text-error">*</span>
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="input input-bordered input-primary w-full bg-base-100/50 text-lg"
                    placeholder="Enter community name"
                    required
                    maxLength={100}
                  />
                  <label className="label">
                    <span className="label-text-alt text-base-content/50">
                      {formData.name.length}/100 characters
                    </span>
                  </label>
                </div>

                {/* Community Slug */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg">Community URL</span>
                    <span className="label-text-alt text-base-content/60">Auto-generated</span>
                  </label>
                  <div className="input-group">
                    <span className="bg-base-200 text-base-content/70 px-4 py-3 rounded-l-lg border border-r-0 border-base-300">
                      comfor.com/communities/
                    </span>
                    <input
                      type="text"
                      name="slug"
                      value={formData.slug}
                      onChange={handleInputChange}
                      className="input input-bordered input-primary flex-1 bg-base-100/50"
                      placeholder="community-url"
                      required
                      maxLength={50}
                      pattern="^[a-z0-9-]+$"
                    />
                  </div>
                  <label className="label">
                    <span className="label-text-alt text-base-content/50">
                      Only lowercase letters, numbers, and hyphens allowed
                    </span>
                  </label>
                </div>

                {/* Community Image Upload */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg">Community Image</span>
                    <span className="label-text-alt text-base-content/60">Optional</span>
                  </label>

                  {/* Drag and Drop Area */}
                  <div
                    className={`border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 ${
                      dragActive
                        ? 'border-primary bg-primary/10'
                        : 'border-base-300 hover:border-primary/50 hover:bg-base-200/50'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    {communityImage ? (
                      <div className="relative">
                        <img
                          src={communityImage.preview}
                          alt="Community preview"
                          className="w-32 h-32 object-cover rounded-xl mx-auto border border-base-300"
                        />
                        <button
                          type="button"
                          onClick={removeImage}
                          className="absolute -top-2 -right-2 btn btn-circle btn-error btn-sm"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                        <p className="text-sm text-base-content/60 mt-4">Click the X to remove or drag a new image to replace</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="text-4xl">🖼️</div>
                        <div>
                          <p className="text-lg font-medium mb-2">Drag and drop an image here</p>
                          <p className="text-base-content/60 mb-4">or</p>
                          <button
                            type="button"
                            onClick={() => fileInputRef.current?.click()}
                            className="btn btn-outline btn-primary"
                          >
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                            </svg>
                            Choose Image
                          </button>
                        </div>
                        <p className="text-sm text-base-content/50">
                          Supports JPG, PNG, GIF, WebP (recommended: 400x400px)
                        </p>
                      </div>
                    )}
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e.target.files)}
                    className="hidden"
                  />
                </div>

                {/* Description */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg">Description</span>
                    <span className="label-text-alt text-error">*</span>
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="textarea textarea-bordered textarea-primary h-32 resize-none bg-base-100/50 text-base"
                    placeholder="Describe what your community is about, its purpose, and what members can expect..."
                    required
                    maxLength={1000}
                  />
                  <label className="label">
                    <span className="label-text-alt text-base-content/50">
                      {formData.description.length}/1000 characters
                    </span>
                  </label>
                </div>

                {/* Privacy Setting */}
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-semibold text-lg">Privacy Setting</span>
                    <span className="label-text-alt text-error">*</span>
                  </label>
                  <div className="space-y-3">
                    <label className="label cursor-pointer justify-start space-x-3 p-4 rounded-lg border border-base-300 hover:border-primary/50 transition-colors">
                      <input
                        type="radio"
                        name="isPrivate"
                        value="false"
                        checked={!formData.isPrivate}
                        onChange={handleInputChange}
                        className="radio radio-primary"
                      />
                      <div>
                        <span className="label-text font-medium text-lg">🌍 Public Community</span>
                        <div className="text-sm text-base-content/70">
                          Anyone can join and view posts
                        </div>
                      </div>
                    </label>
                    <label className="label cursor-pointer justify-start space-x-3 p-4 rounded-lg border border-base-300 hover:border-primary/50 transition-colors">
                      <input
                        type="radio"
                        name="isPrivate"
                        value="true"
                        checked={formData.isPrivate}
                        onChange={handleInputChange}
                        className="radio radio-primary"
                      />
                      <div>
                        <span className="label-text font-medium text-lg">🔒 Private Community</span>
                        <div className="text-sm text-base-content/70">
                          Requires approval to join and view content
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Tags */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium">Tags</span>
                </label>
                <input
                  type="text"
                  name="tags"
                  value={formData.tags}
                  onChange={handleInputChange}
                  className="input input-bordered w-full"
                  placeholder="javascript, react, web-development (comma separated)"
                />
                <label className="label">
                  <span className="label-text-alt text-base-content/60">
                    Help people discover your community with relevant tags
                  </span>
                </label>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="enhanced-card">
            <div className="enhanced-card-body">
              <div className="button-group justify-end">
                <Link href="/communities" className="btn btn-outline btn-lg rounded-full px-8">
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={isSubmitting || !formData.name.trim() || !formData.description.trim()}
                  className="btn btn-primary btn-lg rounded-full px-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  {isSubmitting ? (
                    <>
                      <span className="loading loading-spinner loading-sm"></span>
                      Creating Community...
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Create Community
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
