/* Tailwind v4 + daisyUI v5 base imports */
@import "tailwindcss";
@plugin "daisyui" {
  /* retro default; dark auto preference; additional custom + light + aqua */
  themes: forest --default, dark --prefersdark, chillcity, jumpingdragon, aqua, luxury, forest, light;
  logs: false;
}

/* Retro theme (customized) */

@plugin "daisyui/theme" {
  name: "retro";
  default: false;
  prefersdark: false;
  color-scheme: "light";
  --color-base-100: oklch(91.637% 0.034 90.515);
  --color-base-200: oklch(88.272% 0.049 91.774);
  --color-base-300: oklch(84.133% 0.065 90.856);
  --color-base-content: oklch(41% 0.112 45.904);
  --color-primary: oklch(79% 0.184 86.047);
  --color-primary-content: oklch(39% 0.141 25.723);
  --color-secondary: oklch(72% 0.219 149.579);
  --color-secondary-content: oklch(44% 0.119 151.328);
  --color-accent: oklch(68% 0.162 75.834);
  --color-accent-content: oklch(41% 0.112 45.904);
  --color-neutral: oklch(44% 0.011 73.639);
  --color-neutral-content: oklch(78% 0.154 211.53);
  --color-info: oklch(58% 0.158 241.966);
  --color-info-content: oklch(93% 0.032 255.585);
  --color-success: oklch(51% 0.096 186.391);
  --color-success-content: oklch(96% 0.059 95.617);
  --color-warning: oklch(55% 0.195 38.402);
  --color-warning-content: oklch(96% 0.059 95.617);
  --color-error: oklch(41% 0.159 10.272);
  --color-error-content: oklch(87% 0.169 91.605);
  --radius-selector: 0.5rem;
  --radius-field: 2rem;
  --radius-box: 2rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 0;
  --noise: 0;
}


/* Jumpingdragon playful theme */
@plugin "daisyui/theme" {
  name: "jumpingdragon";
  --noise: 0;
  --color-base-100: oklch(98% 0.031 120.757);
  --color-base-200: oklch(96% 0.067 122.328);
  --color-base-300: oklch(93% 0.127 124.321);
  --color-base-content: oklch(40% 0.101 131.063);
  --color-primary: oklch(0% 0 0);
  --color-primary-content: oklch(100% 0 0);
  --color-secondary: oklch(77% 0.152 181.912);
  --color-secondary-content: oklch(27% 0.046 192.524);
  --color-accent: oklch(76% 0.177 163.223);
  --color-accent-content: oklch(26% 0.051 172.552);
  --color-neutral: oklch(53% 0.157 131.589);
  --color-neutral-content: oklch(98% 0.031 120.757);
  --color-info: oklch(60% 0.126 221.723);
  --color-info-content: oklch(98% 0.019 200.873);
  --color-success: oklch(60% 0.118 184.704);
  --color-success-content: oklch(98% 0.014 180.72);
  --color-warning: oklch(64% 0.222 41.116);
  --color-warning-content: oklch(98% 0.016 73.684);
  --color-error: oklch(58% 0.253 17.585);
  --color-error-content: oklch(96% 0.015 12.422);
  --radius-selector: 2rem;
  --radius-field: 1rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 0;
}


@plugin "daisyui/theme" {
  name: "forest";
  default: true;
  prefersdark: false;
  color-scheme: "dark";
  --color-base-100: oklch(20.84% 0.008 17.911);
  --color-base-200: oklch(18.522% 0.007 17.911);
  --color-base-300: oklch(16.203% 0.007 17.911);
  --color-base-content: oklch(83.768% 0.001 17.911);
  --color-primary: oklch(68.628% 0.185 148.958);
  --color-primary-content: oklch(0% 0 0);
  --color-secondary: oklch(76% 0.233 130.85);
  --color-secondary-content: oklch(13.955% 0.027 168.327);
  --color-accent: oklch(70.628% 0.119 185.713);
  --color-accent-content: oklch(14.125% 0.023 185.713);
  --color-neutral: oklch(30.698% 0.039 171.364);
  --color-neutral-content: oklch(86.139% 0.007 171.364);
  --color-info: oklch(62% 0.214 259.815);
  --color-info-content: oklch(0% 0 0);
  --color-success: oklch(64.8% 0.15 160);
  --color-success-content: oklch(0% 0 0);
  --color-warning: oklch(85% 0.199 91.936);
  --color-warning-content: oklch(0% 0 0);
  --color-error: oklch(63% 0.237 25.331);
  --color-error-content: oklch(0% 0 0);
  --radius-selector: 1rem;
  --radius-field: 2rem;
  --radius-box: 1rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 0;
  --noise: 0;
}


/* Aqua theme */
@plugin "daisyui/theme" {
  name: "aqua";
  default: false;
  prefersdark: false;
  color-scheme: "dark";
  --color-base-100: oklch(37% 0.146 265.522);
  --color-base-200: oklch(28% 0.091 267.935);
  --color-base-300: oklch(22% 0.091 267.935);
  --color-base-content: oklch(90% 0.058 230.902);
  --color-primary: oklch(85.661% 0.144 198.645);
  --color-primary-content: oklch(40.124% 0.068 197.603);
  --color-secondary: oklch(60.682% 0.108 309.782);
  --color-secondary-content: oklch(96% 0.016 293.756);
  --color-accent: oklch(93.426% 0.102 94.555);
  --color-accent-content: oklch(18.685% 0.02 94.555);
  --color-neutral: oklch(27% 0.146 265.522);
  --color-neutral-content: oklch(80% 0.146 265.522);
  --color-info: oklch(54.615% 0.215 262.88);
  --color-info-content: oklch(90.923% 0.043 262.88);
  --color-success: oklch(62.705% 0.169 149.213);
  --color-success-content: oklch(12.541% 0.033 149.213);
  --color-warning: oklch(66.584% 0.157 58.318);
  --color-warning-content: oklch(27% 0.077 45.635);
  --color-error: oklch(73.95% 0.19 27.33);
  --color-error-content: oklch(14.79% 0.038 27.33);
  --radius-selector: 1rem;
  --radius-field: 0.5rem;
  --radius-box: 1rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

@plugin "daisyui/theme" {
  name: "luxury";
  default: false;
  prefersdark: false;
  color-scheme: "dark";
  --color-base-100: oklch(14.076% 0.004 285.822);
  --color-base-200: oklch(20.219% 0.004 308.229);
  --color-base-300: oklch(23.219% 0.004 308.229);
  --color-base-content: oklch(75.687% 0.123 76.89);
  --color-primary: oklch(100% 0 0);
  --color-primary-content: oklch(20% 0 0);
  --color-secondary: oklch(27.581% 0.064 261.069);
  --color-secondary-content: oklch(85.516% 0.012 261.069);
  --color-accent: oklch(36.674% 0.051 338.825);
  --color-accent-content: oklch(87.334% 0.01 338.825);
  --color-neutral: oklch(24.27% 0.057 59.825);
  --color-neutral-content: oklch(93.203% 0.089 90.861);
  --color-info: oklch(79.061% 0.121 237.133);
  --color-info-content: oklch(15.812% 0.024 237.133);
  --color-success: oklch(78.119% 0.192 132.154);
  --color-success-content: oklch(15.623% 0.038 132.154);
  --color-warning: oklch(86.127% 0.136 102.891);
  --color-warning-content: oklch(17.225% 0.027 102.891);
  --color-error: oklch(71.753% 0.176 22.568);
  --color-error-content: oklch(14.35% 0.035 22.568);
  --radius-selector: 1rem;
  --radius-field: 0.5rem;
  --radius-box: 1rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

@plugin "daisyui/theme" {
  name: "chillcity";
  default: false;
  prefersdark: false;
  color-scheme: "dark";
  --color-base-100: oklch(14% 0 0);
  --color-base-200: oklch(20% 0 0);
  --color-base-300: oklch(26% 0 0);
  --color-base-content: oklch(87% 0 0);
  --color-primary: oklch(54% 0.245 262.881);
  --color-primary-content: oklch(97% 0.014 254.604);
  --color-secondary: oklch(91% 0.096 180.426);
  --color-secondary-content: oklch(50% 0.134 242.749);
  --color-accent: oklch(44% 0.11 240.79);
  --color-accent-content: oklch(97% 0.014 308.299);
  --color-neutral: oklch(60% 0.126 221.723);
  --color-neutral-content: oklch(98% 0 0);
  --color-info: oklch(70% 0.165 254.624);
  --color-info-content: oklch(37% 0.146 265.522);
  --color-success: oklch(79% 0.209 151.711);
  --color-success-content: oklch(26% 0.065 152.934);
  --color-warning: oklch(85% 0.199 91.936);
  --color-warning-content: oklch(47% 0.114 61.907);
  --color-error: oklch(63% 0.237 25.331);
  --color-error-content: oklch(28% 0.091 267.935);
  --radius-selector: 1rem;
  --radius-field: 1rem;
  --radius-box: 1rem;
  --size-selector: 0.28125rem;
  --size-field: 0.21875rem;
  --border: 1px;
  --depth: 1;
  --noise: 1;
}



/* Custom animations and styles for modern look */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }

  .backdrop-blur-3xl {
    backdrop-filter: blur(64px);
  }

  /* Slow spin animation utility */
  @keyframes spin-slow { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
  .animate-spin-slow { animation: spin-slow 8s linear infinite; }

  /* Enhanced responsive spacing utilities */
  .spacing-xs { @apply p-2 sm:p-3; }
  .spacing-sm { @apply p-3 sm:p-4 lg:p-6; }
  .spacing-md { @apply p-4 sm:p-6 lg:p-8; }
  .spacing-lg { @apply p-6 sm:p-8 lg:p-12; }
  .spacing-xl { @apply p-8 sm:p-12 lg:p-16; }

  .margin-xs { @apply m-2 sm:m-3; }
  .margin-sm { @apply m-3 sm:m-4 lg:m-6; }
  .margin-md { @apply m-4 sm:m-6 lg:m-8; }
  .margin-lg { @apply m-6 sm:m-8 lg:m-12; }
  .margin-xl { @apply m-8 sm:m-12 lg:m-16; }
}

@layer components {
  .btn {
    @apply transition-all duration-300 rounded-lg;
  }

  /* Ensure all buttons have rounded corners by default */
  .btn:not(.btn-square):not(.btn-circle) {
    @apply rounded-lg;
  }

  /* Special rounded variants */
  .btn-rounded {
    @apply rounded-full;
  }

  /* Standardized Tab Styling */
  .tabs {
    @apply mb-6;
  }

  .tabs.tabs-boxed {
    @apply bg-base-100/80 backdrop-blur-sm shadow-xl border border-primary/10 p-2 rounded-2xl;
  }

  .tab {
    @apply px-6 py-3 rounded-xl font-medium transition-all duration-300;
  }

  .tab-lg {
    @apply px-8 py-4 text-base;
  }

  .tab-sm {
    @apply px-4 py-2 text-sm;
  }

  .tab-active {
    @apply bg-gradient-to-r from-primary/10 to-accent/10 text-primary font-semibold shadow-sm;
  }

  /* Tab Content Areas */
  .tab-content {
    @apply p-6 space-y-6;
  }

  .tab-content-lg {
    @apply p-8 space-y-8;
  }

  .tab-content-sm {
    @apply p-4 space-y-4;
  }

  /* Vertical Tabs */
  .tabs-vertical {
    @apply flex flex-col space-y-2;
  }

  .tabs-vertical .tab {
    @apply w-full justify-start text-left;
  }

  .card {
    @apply transition-all duration-300;
  }

  /* Enhanced layout components */
  .page-container {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12;
  }

  .enhanced-card {
    @apply card shadow-xl border transition-all duration-300;
    background: linear-gradient(to bottom right, hsl(var(--b1)), hsl(var(--b2) / 0.5));
    border-color: hsl(var(--p) / 0.1);
  }

  .enhanced-card:hover {
    border-color: hsl(var(--p) / 0.2);
  }

  .enhanced-card-body {
    @apply card-body p-6 sm:p-8 lg:p-10;
  }

  /* Section spacing */
  .section-spacing {
    @apply mb-8 sm:mb-12 lg:mb-16;
  }

  .page-header {
    @apply mb-6 sm:mb-8 lg:mb-12;
  }

  .content-spacing {
    @apply space-y-6 sm:space-y-8;
  }

  .form-section {
    @apply space-y-6 sm:space-y-8;
  }

  .button-group {
    @apply flex flex-col sm:flex-row gap-3 sm:gap-4;
  }

  .grid-spacing {
    @apply gap-4 sm:gap-6 lg:gap-8;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: oklch(var(--b2));
}

::-webkit-scrollbar-thumb {
  background: oklch(var(--p) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: oklch(var(--p) / 0.5);
}

/* Gradient text animation */
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 3s ease infinite;
}

/* Pulse animation for live indicators */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Sidebar slide animations with bounce effect */
@keyframes slide-in-left {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  70% {
    transform: translateX(2%);
    opacity: 1;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-left {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  30% {
    transform: translateX(-2%);
    opacity: 0.8;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.animate-slide-in-left {
  animation: slide-in-left 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.animate-slide-out-left {
  animation: slide-out-left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Smooth content shift animation */
@keyframes content-shift-right {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(320px);
  }
}

.animate-content-shift {
  animation: content-shift-right 0.3s ease-out forwards;
}

/* Ensure all interactive elements are clickable */
button, a, [role="button"], [tabindex] {
  cursor: pointer;
  user-select: none;
}

/* Fix dropdown positioning and z-index */
.dropdown-content {
  position: absolute;
  z-index: 50;
}

/* Ensure buttons have proper pointer events */
.btn {
  pointer-events: auto;
}

/* Fix menu items */
.menu li > * {
  cursor: pointer;
}

/* Mobile spacing overrides for better small-screen ergonomics */
@media (max-width: 640px) {
  /* Buttons: reduce overly-wide horizontal padding on small screens */
  .btn[class~="px-8"],
  .btn.btn-lg[class~="px-8"] {
    padding-left: 1.5rem; /* px-6 */
    padding-right: 1.5rem; /* px-6 */
  }
  /* Large buttons without explicit px- utility: provide comfortable padding */
  .btn.btn-lg:not([class*="px-"]) {
    padding-left: 1.5rem; /* px-6 */
    padding-right: 1.5rem; /* px-6 */
  }

  /* Card bodies: scale down large paddings */
  .card-body[class~="p-12"] {
    padding: 2rem; /* p-8 */
  }
  .card-body[class~="p-10"] {
    padding: 2rem; /* p-8 */
  }
  .card-body[class~="p-8"] {
    padding: 1.5rem; /* p-6 */
  }
  .card-body[class~="py-16"] {
    padding-top: 2rem; /* py-8 */
    padding-bottom: 2rem; /* py-8 */
  }
  .card-body[class~="py-12"] {
    padding-top: 2rem; /* py-8 */
    padding-bottom: 2rem; /* py-8 */
  }

  /* Section-level spacing tightening */
  .section-spacing {
    margin-bottom: 1.5rem; /* mb-6 */
  }
  .page-header {
    margin-bottom: 1rem; /* mb-4 */
  }

  /* Reduce default grid gaps slightly on mobile */
  .grid-spacing {
    gap: 1rem; /* gap-4 */
  }
}
