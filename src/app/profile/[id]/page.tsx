"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { getRouteParam } from '@/lib/params';

// Types
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  image: string;
  bio: string;
  createdAt: string;
  updatedAt: string;
  posts: Post[];
  _count: {
    posts: number;
    comments: number;
    likes: number;
    followers: number;
    following: number;
  };
}

interface Post {
  id: string;
  title: string;
  content: string;
  published: boolean;
  createdAt: string;
  images?: string[]; // optional array of image URLs
  videoUrl?: string | null; // optional video URL
  _count: {
    comments: number;
    likes: number;
  };
}

export default function UserProfilePage() {
  const { data: session } = useSession();
  const params = useParams();
  const userId = getRouteParam(params, 'id');
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"posts" | "activity">("posts");

  // Fetch user data
  const fetchUser = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/users/${userId}`);
      if (!response.ok) {
        throw new Error('User not found');
      }
      const data: User = await response.json();
      setUser(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user');
    } finally {
      setLoading(false);
    }
  };

  // Load user on component mount
  useEffect(() => {
    if (userId) {
      fetchUser();
    }
  }, [userId]);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "Less than an hour ago";
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "ADMIN": return "badge-error";
      case "EDITOR": return "badge-warning";
      default: return "badge-info";
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
        <div className="container mx-auto px-4 py-8">
          <div className="card bg-base-100 shadow-xl border border-base-300">
            <div className="card-body text-center py-16">
              <div className="loading loading-spinner loading-lg text-primary"></div>
              <p className="text-base-content/60 mt-4">Loading profile...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
        <div className="container mx-auto px-4 py-8">
          <div className="card bg-base-100 shadow-xl border border-error/20">
            <div className="card-body text-center py-16">
              <div className="text-6xl mb-4">👤</div>
              <h3 className="text-2xl font-bold mb-2 text-error">User Not Found</h3>
              <p className="text-base-content/60 mb-6">{error || 'The user profile you are looking for does not exist.'}</p>
              <Link href="/posts" className="btn btn-primary">
                Back to Posts
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300">
      <div className="container mx-auto px-4 py-8">
        {/* Profile Header */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10 mb-8">
          <div className="card-body p-8">
            <div className="flex flex-col lg:flex-row items-center lg:items-start gap-8">
              {/* Avatar */}
              <div className="avatar">
                <div className="w-32 h-32 rounded-full ring-4 ring-primary/20 ring-offset-4 ring-offset-base-100">
                  <img
                    src={user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=random&size=128`}
                    alt={user.name}
                    className="rounded-full"
                  />
                </div>
              </div>

              {/* User Info */}
              <div className="flex-1 text-center lg:text-left">
                <div className="flex flex-col lg:flex-row lg:items-center gap-4 mb-4">
                  <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
                    {user.name}
                  </h1>
                  <div className="flex items-center justify-center lg:justify-start gap-2">
                    <span className={`badge ${getRoleBadgeColor(user.role)} badge-lg`}>
                      {user.role}
                    </span>
                    {session?.user?.email === user.email && (
                      <span className="badge badge-outline badge-lg">You</span>
                    )}
                  </div>
                </div>

                {user.bio && (
                  <p className="text-lg text-base-content/70 mb-6 max-w-2xl">
                    {user.bio}
                  </p>
                )}

                {/* Stats */}
                <div className="stats shadow-lg bg-gradient-to-r from-primary/5 to-accent/5 mb-6">
                  <div className="stat">
                    <div className="stat-title text-xs">Posts</div>
                    <div className="stat-value text-2xl text-primary">{user._count.posts}</div>
                  </div>
                  <div className="stat">
                    <div className="stat-title text-xs">Followers</div>
                    <div className="stat-value text-2xl text-accent">{user._count.followers || 0}</div>
                  </div>
                  <div className="stat">
                    <div className="stat-title text-xs">Following</div>
                    <div className="stat-value text-2xl text-secondary">{user._count.following || 0}</div>
                  </div>
                  <div className="stat">
                    <div className="stat-title text-xs">Likes Given</div>
                    <div className="stat-value text-2xl text-info">{user._count.likes}</div>
                  </div>
                </div>

                <div className="text-sm text-base-content/60">
                  Member since {formatTimeAgo(user.createdAt)}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="tabs tabs-boxed bg-base-100/80 backdrop-blur-sm shadow-xl border border-primary/10 mb-8">
          <button
            className={`tab tab-lg ${activeTab === "posts" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("posts")}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Posts ({user._count.posts})
          </button>
          <button
            className={`tab tab-lg ${activeTab === "activity" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("activity")}
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            Activity
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === "posts" ? (
          <div className="space-y-6">
            {user.posts && user.posts.length > 0 ? (
              user.posts.map((post) => {
                // Extract up to 5 unique hashtag tags from content
                const tags = Array.from(
                  new Set(
                    (post.content.match(/(^|\s)#(\w+)/g) || [])
                      .map(t => t.trim().replace(/^#/, '').toLowerCase())
                  )
                ).slice(0, 5);
                return (
                <div key={post.id} className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl hover:shadow-2xl transition-all duration-300 border border-primary/5 hover:border-primary/20">
                  <div className="card-body">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="card-title text-xl mb-2 hover:text-primary transition-colors">
                          <Link href={`/posts/${post.id}`} className="hover:underline">
                            {post.title}
                          </Link>
                        </h3>
                        <p className="text-base-content/70 line-clamp-3 mb-2">
                          {post.content.substring(0, 200)}...
                        </p>

                        {/* Tags (hashtags) */}
                        {tags.length > 0 && (
                          <div className="flex flex-wrap gap-2 mb-3">
                            {tags.map(tag => (
                              <span key={tag} className="badge badge-outline badge-xs">
                                #{tag}
                              </span>
                            ))}
                          </div>
                        )}

                        {/* Media indicators */}
                        {(post.images?.length || post.videoUrl) && (
                          <div className="flex items-center space-x-3 mb-1 text-xs">
                            {post.images?.length ? (
                              <span className="flex items-center space-x-1 text-accent">
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span>{post.images.length}</span>
                              </span>
                            ) : null}
                            {post.videoUrl && (
                              <span className="flex items-center space-x-1 text-secondary">
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                                <span>video</span>
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-base-content/60">
                        <span>{formatTimeAgo(post.createdAt)}</span>
                        <span className="flex items-center space-x-1">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                          <span>{post._count.likes}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          <span>{post._count.comments}</span>
                        </span>
                      </div>

                      <Link href={`/posts/${post.id}`} className="btn btn-primary btn-sm">
                        Read More
                      </Link>
                    </div>
                  </div>
                </div>
                );
              })
            ) : (
              <div className="card bg-base-100 shadow-xl border border-base-300">
                <div className="card-body text-center py-16">
                  <div className="text-6xl mb-4">📝</div>
                  <h3 className="text-2xl font-bold mb-2">No Posts Yet</h3>
                  <p className="text-base-content/60">
                    {session?.user?.email === user.email 
                      ? "You haven't created any posts yet. Start sharing your thoughts!"
                      : `${user.name} hasn't created any posts yet.`
                    }
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="card bg-base-100 shadow-xl border border-base-300">
            <div className="card-body text-center py-16">
              <div className="text-6xl mb-4">🚧</div>
              <h3 className="text-2xl font-bold mb-2">Activity Feed Coming Soon</h3>
              <p className="text-base-content/60">
                We're working on a comprehensive activity feed to show user interactions, comments, and more.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
