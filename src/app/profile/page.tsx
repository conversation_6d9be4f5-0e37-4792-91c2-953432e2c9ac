"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import ProfilePictureUpload from "@/components/ProfilePictureUpload";

// Types for user profile data
interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  image?: string;
  bio?: string;
  isPrivate: boolean;
  showEmail: boolean;
  createdAt: string;
  updatedAt: string;
  _count: {
    posts: number;
    comments: number;
    likes: number;
    communities: number;
    communityMemberships: number;
  };
  posts: Array<{
    id: string;
    title: string;
    content: string;
    published: boolean;
    createdAt: string;
    updatedAt: string;
    community?: {
      id: string;
      name: string;
      slug: string;
    };
    _count: {
      comments: number;
      likes: number;
    };
  }>;
  comments: Array<{
    id: string;
    content: string;
    createdAt: string;
    post: {
      id: string;
      title: string;
    };
    _count: {
      likes: number;
    };
  }>;
  communityMemberships: Array<{
    community: {
      id: string;
      name: string;
      slug: string;
      image?: string;
    };
  }>;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [editForm, setEditForm] = useState({
    name: "",
    bio: "",
    isPrivate: false,
    showEmail: false,
  });
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch user profile data
  useEffect(() => {
    const fetchProfile = async () => {
      if (!session?.user) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await fetch('/api/profile');
        
        if (response.ok) {
          const profileData = await response.json();
          setUserProfile(profileData);
          setEditForm({
            name: profileData.name || "",
            bio: profileData.bio || "",
            isPrivate: profileData.isPrivate || false,
            showEmail: profileData.showEmail || false,
          });
        } else {
          setError('Failed to load profile');
        }
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError('Failed to load profile');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [session]);

  // Handle profile update
  const handleUpdateProfile = async () => {
    if (!userProfile) return;

    try {
      setIsUpdating(true);
      const response = await fetch('/api/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (response.ok) {
        const updatedProfile = await response.json();
        setUserProfile({ ...userProfile, ...updatedProfile });
        setIsEditing(false);
        alert('Profile updated successfully!');
      } else {
        const errorData = await response.json();
        alert(`Failed to update profile: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile');
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle profile picture update
  const handleProfilePictureUpdate = async (imageUrl: string) => {
    if (!userProfile) return;

    try {
      const response = await fetch('/api/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ image: imageUrl }),
      });

      if (response.ok) {
        const updatedProfile = await response.json();
        setUserProfile({ ...userProfile, image: updatedProfile.image });
        alert('Profile picture updated successfully!');
      } else {
        alert('Failed to update profile picture');
      }
    } catch (error) {
      console.error('Error updating profile picture:', error);
      alert('Failed to update profile picture');
    }
  };

  // Format time ago
  const formatTimeAgo = (date: string) => {
    const now = new Date();
    const past = new Date(date);
    const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return past.toLocaleDateString();
  };

  // Redirect if not authenticated
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (!session) {
    router.push('/auth/signin');
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (error || !userProfile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-error mb-4">Error Loading Profile</h1>
          <p className="text-base-content/70 mb-4">{error || 'Profile not found'}</p>
          <button
            className="btn btn-primary rounded-lg"
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200/30 to-base-300/20">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Profile Header */}
        <div className="card bg-base-100 shadow-xl mb-8">
          <div className="card-body">
            <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
              {/* Profile Picture */}
              <div className="flex-shrink-0">
                <ProfilePictureUpload
                  currentImage={userProfile.image}
                  onImageUpdate={handleProfilePictureUpdate}
                  size="lg"
                />
              </div>

              {/* Profile Info */}
              <div className="flex-grow">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <h1 className="text-3xl font-bold">{userProfile.name}</h1>
                    <p className="text-base-content/70 text-lg">@{userProfile.name?.toLowerCase().replace(/\s+/g, '')}</p>
                    <div className="flex items-center gap-2 mt-2">
                      <span className={`badge ${userProfile.role === 'ADMIN' ? 'badge-error' : userProfile.role === 'EDITOR' ? 'badge-warning' : 'badge-info'}`}>
                        {userProfile.role}
                      </span>
                      <span className="text-sm text-base-content/60">
                        Joined {formatTimeAgo(userProfile.createdAt)}
                      </span>
                    </div>
                  </div>

                  <button
                    className="btn btn-outline btn-sm rounded-lg"
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Edit Profile
                  </button>
                </div>

                {/* Bio */}
                {userProfile.bio && (
                  <p className="mt-4 text-base-content/80">{userProfile.bio}</p>
                )}

                {/* Stats */}
                <div className="flex flex-wrap gap-6 mt-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{userProfile._count.posts}</div>
                    <div className="text-sm text-base-content/60">Posts</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-secondary">{userProfile._count.comments}</div>
                    <div className="text-sm text-base-content/60">Comments</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-accent">{userProfile._count.likes}</div>
                    <div className="text-sm text-base-content/60">Likes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-info">{userProfile._count.communityMemberships}</div>
                    <div className="text-sm text-base-content/60">Communities</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Edit Profile Modal */}
        {isEditing && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="card bg-base-100 w-full max-w-md">
              <div className="card-body">
                <h2 className="card-title">Edit Profile</h2>

                <div className="space-y-4">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Name</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered"
                      value={editForm.name}
                      onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                    />
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Bio</span>
                    </label>
                    <textarea
                      className="textarea textarea-bordered"
                      rows={3}
                      value={editForm.bio}
                      onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}
                      placeholder="Tell us about yourself..."
                    />
                  </div>

                  <div className="form-control">
                    <label className="cursor-pointer label">
                      <span className="label-text">Private Profile</span>
                      <input
                        type="checkbox"
                        className="toggle toggle-primary"
                        checked={editForm.isPrivate}
                        onChange={(e) => setEditForm({ ...editForm, isPrivate: e.target.checked })}
                      />
                    </label>
                  </div>

                  <div className="form-control">
                    <label className="cursor-pointer label">
                      <span className="label-text">Show Email</span>
                      <input
                        type="checkbox"
                        className="toggle toggle-primary"
                        checked={editForm.showEmail}
                        onChange={(e) => setEditForm({ ...editForm, showEmail: e.target.checked })}
                      />
                    </label>
                  </div>
                </div>

                <div className="card-actions justify-end mt-6">
                  <button
                    className="btn btn-ghost rounded-lg"
                    onClick={() => setIsEditing(false)}
                    disabled={isUpdating}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn btn-primary rounded-lg"
                    onClick={handleUpdateProfile}
                    disabled={isUpdating}
                  >
                    {isUpdating ? (
                      <>
                        <span className="loading loading-spinner loading-sm"></span>
                        Updating...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="tabs tabs-boxed bg-base-200 mb-6">
          <button
            className={`tab ${activeTab === "overview" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("overview")}
          >
            Overview
          </button>
          <button
            className={`tab ${activeTab === "posts" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("posts")}
          >
            Posts ({userProfile._count.posts})
          </button>
          <button
            className={`tab ${activeTab === "comments" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("comments")}
          >
            Comments ({userProfile._count.comments})
          </button>
          <button
            className={`tab ${activeTab === "communities" ? "tab-active" : ""}`}
            onClick={() => setActiveTab("communities")}
          >
            Communities ({userProfile._count.communityMemberships})
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === "overview" && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Posts */}
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h2 className="card-title">Recent Posts</h2>
                {userProfile.posts.length > 0 ? (
                  <div className="space-y-4">
                    {userProfile.posts.map((post) => (
                      <div key={post.id} className="border-l-4 border-primary pl-4">
                        <Link href={`/posts/${post.id}`} className="hover:text-primary">
                          <h3 className="font-semibold">{post.title}</h3>
                        </Link>
                        <p className="text-sm text-base-content/70 mt-1">
                          {post.content.substring(0, 100)}...
                        </p>
                        <div className="flex items-center gap-4 mt-2 text-xs text-base-content/60">
                          <span>{formatTimeAgo(post.createdAt)}</span>
                          <span>{post._count.likes} likes</span>
                          <span>{post._count.comments} comments</span>
                          {post.community && (
                            <span>in {post.community.name}</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-base-content/60">No posts yet</p>
                )}
              </div>
            </div>

            {/* Recent Comments */}
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h2 className="card-title">Recent Comments</h2>
                {userProfile.comments.length > 0 ? (
                  <div className="space-y-4">
                    {userProfile.comments.map((comment) => (
                      <div key={comment.id} className="border-l-4 border-secondary pl-4">
                        <p className="text-sm">{comment.content.substring(0, 100)}...</p>
                        <div className="flex items-center gap-4 mt-2 text-xs text-base-content/60">
                          <span>{formatTimeAgo(comment.createdAt)}</span>
                          <span>{comment._count.likes} likes</span>
                          <Link href={`/posts/${comment.post.id}`} className="hover:text-primary">
                            on "{comment.post.title}"
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-base-content/60">No comments yet</p>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === "posts" && (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">All Posts</h2>
              {userProfile.posts.length > 0 ? (
                <div className="space-y-6">
                  {userProfile.posts.map((post) => (
                    <div key={post.id} className="border-b border-base-300 pb-6 last:border-b-0">
                      <Link href={`/posts/${post.id}`} className="hover:text-primary">
                        <h3 className="text-xl font-semibold mb-2">{post.title}</h3>
                      </Link>
                      <p className="text-base-content/80 mb-3">
                        {post.content.substring(0, 200)}...
                      </p>
                      <div className="flex items-center gap-4 text-sm text-base-content/60">
                        <span>{formatTimeAgo(post.createdAt)}</span>
                        <span className={`badge ${post.published ? 'badge-success' : 'badge-warning'}`}>
                          {post.published ? 'Published' : 'Draft'}
                        </span>
                        <span>{post._count.likes} likes</span>
                        <span>{post._count.comments} comments</span>
                        {post.community && (
                          <span>in {post.community.name}</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-base-content/60 mb-4">No posts yet</p>
                  <Link href="/posts/create" className="btn btn-primary rounded-lg">
                    Create Your First Post
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === "comments" && (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">All Comments</h2>
              {userProfile.comments.length > 0 ? (
                <div className="space-y-4">
                  {userProfile.comments.map((comment) => (
                    <div key={comment.id} className="border-l-4 border-secondary pl-4 py-3">
                      <p className="mb-2">{comment.content}</p>
                      <div className="flex items-center gap-4 text-sm text-base-content/60">
                        <span>{formatTimeAgo(comment.createdAt)}</span>
                        <span>{comment._count.likes} likes</span>
                        <Link href={`/posts/${comment.post.id}`} className="hover:text-primary">
                          on "{comment.post.title}"
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-base-content/60">No comments yet</p>
              )}
            </div>
          </div>
        )}

        {activeTab === "communities" && (
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <h2 className="card-title">Communities</h2>
              {userProfile.communityMemberships.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {userProfile.communityMemberships.map((membership) => (
                    <Link
                      key={membership.community.id}
                      href={`/communities/${membership.community.slug}`}
                      className="card bg-base-200 hover:bg-base-300 transition-colors"
                    >
                      <div className="card-body p-4">
                        <div className="flex items-center gap-3">
                          {membership.community.image ? (
                            <img
                              src={membership.community.image}
                              alt={membership.community.name}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                          ) : (
                            <div className="w-12 h-12 bg-gradient-to-br from-primary/30 to-secondary/30 rounded-lg flex items-center justify-center">
                              <span className="text-2xl">🏘️</span>
                            </div>
                          )}
                          <div>
                            <h3 className="font-semibold">{membership.community.name}</h3>
                            <p className="text-sm text-base-content/60">Member</p>
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-base-content/60 mb-4">Not a member of any communities yet</p>
                  <Link href="/communities" className="btn btn-primary rounded-lg">
                    Explore Communities
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
