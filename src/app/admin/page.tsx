"use client";

import { useState, useEffect, useCallback } from "react";
import Image from 'next/image';
import { useSession } from "next-auth/react";
import { formatTimeAgo, getRoleBadgeColor } from '@/lib/adminUtils';
import Link from "next/link";

// Types for admin data
interface AdminStats {
  totalUsers: number;
  totalPosts: number;
  totalComments: number;
  totalLikes: number;
  totalCommunities: number;
  newUsersToday: number;
  newPostsToday: number;
  newCommentsToday: number;
  postsThisWeek: number;
  commentsThisWeek: number;
  roleDistribution: Record<string, number>;
  recentPosts: Array<{
    id: string;
    title: string;
    author: string;
    authorRole: string;
    createdAt: string;
    published: boolean;
    likes: number;
    comments: number;
  }>;
  avgPostsPerUser: number;
  avgCommentsPerPost: number;
  engagementRate: number;
}

interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: string;
  image?: string;
  createdAt: string;
  updatedAt: string;
  _count: {
    posts: number;
    comments: number;
    likes: number;
  };
  bio?: string | null;
}

interface AdminPost {
  id: string;
  title: string;
  content: string;
  published: boolean;
  featured?: boolean;
  pinned?: boolean;
  locked?: boolean;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  community?: {
    id: string;
    name: string;
    slug: string;
  };
  _count: {
    comments: number;
    likes: number;
  };
}

interface AdminReport {
  id: string;
  type: string;
  reason: string;
  description?: string;
  status: string;
  createdAt: string;
  reporter: {
    id: string;
    name: string;
    email: string;
  };
  post?: {
    id: string;
    title: string;
  };
  comment?: {
    id: string;
    content: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

interface ForumSetting {
  id: string;
  key: string;
  value: string;
  type: string;
  category: string;
  description?: string;
  isPublic: boolean;
}

// Added more specific types to replace many `any` usages below.
interface AdminCommunity {
  id: string;
  name: string;
  slug: string;
  description: string;
  image?: string | null;
  createdAt: string;
  updatedAt: string;
  memberCount?: number;
  postCount?: number;
  isPrivate?: boolean;
  allowPosts?: boolean;
  requireApproval?: boolean;
  archived?: boolean;
  featured?: boolean;
  rules?: string | null;
  creator?: { id: string; name: string; email: string; role: string };
  _count?: { members?: number; posts?: number };
}

interface AdminComment {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  author: { id: string; name: string; email: string; role: string; image?: string };
  post?: { id: string; title: string };
  parentId?: string | null;
  _count?: { likes: number; replies?: number };
  flagged?: boolean;
  deleted?: boolean;
}

interface AnalyticsOverview {
  totalUsers: number; activeUsers: number; totalPosts: number; totalComments: number; totalLikes: number; engagementRate: number;
}
interface AnalyticsPoint { date: string; users?: number; engagement?: number }
interface TopUser { id: string; name: string; totalActivity: number }
interface TopCommunity { id: string; name: string; activity: number }
interface AdminAnalytics {
  overview: AnalyticsOverview;
  charts: { userGrowth: AnalyticsPoint[]; dailyEngagement: AnalyticsPoint[] };
  topUsers: TopUser[];
  topCommunities: TopCommunity[];
}

export default function AdminDashboard() {
  const { data: session, status } = useSession();
  const isAdmin = !!(session?.user && (session.user as { role?: string }).role === 'ADMIN');
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [posts, setPosts] = useState<AdminPost[]>([]);
  const [reports, setReports] = useState<AdminReport[]>([]);
  const [reportStats, setReportStats] = useState<{ totalReports: number; pendingReports: number; reviewedReports: number; resolvedReports: number; dismissedReports: number; reportsToday: number } | null>(null);
  const [reportsLoading, setReportsLoading] = useState(false);
  const [reportsError, setReportsError] = useState<string | null>(null);
  const [reportFilters, setReportFilters] = useState<{ status?: string; type?: string; page: number; limit: number }>({ page: 1, limit: 20 });
  const [selectedReportIds, setSelectedReportIds] = useState<Set<string>>(new Set());
  const [settings, setSettings] = useState<ForumSetting[]>([]);
  const [groupedSettings, setGroupedSettings] = useState<Record<string, ForumSetting[]>>({});
  const [settingsLoading, setSettingsLoading] = useState(false);
  const [settingsDirty, setSettingsDirty] = useState<Record<string, string>>({});
  const [settingsError, setSettingsError] = useState<string | null>(null);
  const [communities, setCommunities] = useState<AdminCommunity[]>([]);
  const [editingCommunity, setEditingCommunity] = useState<AdminCommunity | null>(null);
  const [editingUser, setEditingUser] = useState<AdminUser | null>(null);
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [analytics, setAnalytics] = useState<AdminAnalytics | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [analyticsError, setAnalyticsError] = useState<string | null>(null);
  const [analyticsPeriod, setAnalyticsPeriod] = useState<number>(30);
  // Posts moderation state
  const [postsLoading, setPostsLoading] = useState(false);
  const [postsError, setPostsError] = useState<string | null>(null);
  const [postFilters, setPostFilters] = useState<{ page: number; limit: number; search?: string }>({ page: 1, limit: 20 });
  const [selectedPostIds, setSelectedPostIds] = useState<Set<string>>(new Set());
  const [editingPost, setEditingPost] = useState<AdminPost | null>(null);
  // Comments moderation state
  const [comments, setComments] = useState<AdminComment[]>([]);
  const [commentsLoading, setCommentsLoading] = useState(false);
  const [commentsError, setCommentsError] = useState<string | null>(null);
  const [commentFilters, setCommentFilters] = useState<{ page: number; limit: number; search?: string; postId?: string; parentOnly?: boolean }>({ page: 1, limit: 20, parentOnly: false });
  const [selectedCommentIds, setSelectedCommentIds] = useState<Set<string>>(new Set());
  const [editingComment, setEditingComment] = useState<AdminComment | null>(null);

  // ---------- Data Fetch Helpers ----------
  const fetchReports = useCallback(async (opts?: Partial<typeof reportFilters>) => {
    if (!isAdmin) return;
    const merged = { ...reportFilters, ...(opts || {}) };
    setReportFilters(merged);
    setReportsLoading(true);
    setReportsError(null);
    try {
      const params = new URLSearchParams();
      params.set('page', String(merged.page));
      params.set('limit', String(merged.limit));
      if (merged.status) params.set('status', merged.status);
      if (merged.type) params.set('type', merged.type);
      const res = await fetch(`/api/admin/reports?${params.toString()}`);
      if (!res.ok) throw new Error(`${res.status}`);
      const json = await res.json();
      setReports(json.reports || []);
      setReportStats(json.stats || null);
      setSelectedReportIds(new Set());
  } catch {
      setReportsError('Failed to load reports');
    } finally {
      setReportsLoading(false);
    }
  }, [reportFilters, isAdmin]);

  const updateReportStatus = useCallback(async (ids: string[], status: string) => {
    if (!ids.length) return;
    try {
      const res = await fetch('/api/admin/reports', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reportIds: ids, status })
      });
      if (!res.ok) throw new Error('Failed');
      await fetchReports();
  } catch {
      alert('Failed to update reports');
    }
  }, [fetchReports]);

  const fetchAnalytics = useCallback(async (period?: number) => {
    if (!isAdmin) return;
    const days = period ?? analyticsPeriod;
    setAnalyticsPeriod(days);
    setAnalyticsLoading(true);
    setAnalyticsError(null);
    try {
      const res = await fetch(`/api/admin/analytics?period=${days}`);
      if (!res.ok) throw new Error(`${res.status}`);
      const json = await res.json();
      setAnalytics(json.analytics);
  } catch {
      setAnalyticsError('Failed to load analytics');
    } finally {
      setAnalyticsLoading(false);
    }
  }, [analyticsPeriod, isAdmin]);

  const fetchSettings = useCallback(async () => {
    if (!isAdmin) return;
    setSettingsLoading(true);
    setSettingsError(null);
    try {
      const res = await fetch('/api/admin/settings');
      if (!res.ok) throw new Error(`${res.status}`);
      const json = await res.json();
      setSettings(json.settings || []);
      setGroupedSettings(json.groupedSettings || {});
      setSettingsDirty({});
    } catch {
      setSettingsError('Failed to load settings');
    } finally {
      setSettingsLoading(false);
    }
  }, [isAdmin]);

  const saveDirtySettings = useCallback(async () => {
    if (!Object.keys(settingsDirty).length) return;
    try {
      const payload = Object.entries(settingsDirty).map(([key, value]) => ({ key, value }));
      const res = await fetch('/api/admin/settings', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ settings: payload })
      });
      if (!res.ok) throw new Error('Failed');
      await fetchSettings();
  } catch {
      alert('Failed to save settings');
    }
  }, [settingsDirty, fetchSettings]);

  // Trigger data fetches when tab changes (lazy load)
  useEffect(() => {
    if (activeTab === 'reports' && reports.length === 0 && !reportsLoading) fetchReports();
    if (activeTab === 'analytics' && !analytics && !analyticsLoading) fetchAnalytics();
    if (activeTab === 'settings' && settings.length === 0 && !settingsLoading) fetchSettings();
  }, [activeTab, fetchReports, fetchAnalytics, fetchSettings, reports.length, analytics, settings.length, reportsLoading, analyticsLoading, settingsLoading]);

  // ---------- Posts Moderation Helpers ----------
  const fetchPosts = useCallback(async (opts?: Partial<typeof postFilters>) => {
    if (!isAdmin) return;
    const merged = { ...postFilters, ...(opts || {}) };
    setPostFilters(merged);
    setPostsLoading(true);
    setPostsError(null);
    try {
      const params = new URLSearchParams();
      params.set('page', String(merged.page));
      params.set('limit', String(merged.limit));
      if (merged.search) params.set('search', merged.search);
      const res = await fetch(`/api/admin/posts?${params.toString()}`);
      if (!res.ok) throw new Error(`${res.status}`);
      const json = await res.json();
      setPosts(json.posts || []);
      setSelectedPostIds(new Set());
    } catch {
      setPostsError('Failed to load posts');
    } finally {
      setPostsLoading(false);
    }
  }, [postFilters, isAdmin]);

  type PostPatch = Partial<Pick<AdminPost,'title'|'content'|'published'|'featured'|'pinned'|'locked'>>;
  const patchPost = useCallback(async (id: string, data: PostPatch) => {
    try {
      const res = await fetch(`/api/admin/posts/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!res.ok) throw new Error('Failed');
      await fetchPosts();
      setEditingPost(null);
    } catch {
      alert('Failed to update post');
    }
  }, [fetchPosts]);

  const deletePost = useCallback(async (id: string) => {
    if (!confirm('Delete this post? This cannot be undone.')) return;
    try {
      const res = await fetch(`/api/admin/posts/${id}`, { method: 'DELETE' });
      if (!res.ok) throw new Error('Failed');
      await fetchPosts();
    } catch {
      alert('Failed to delete post');
    }
  }, [fetchPosts]);

  // ---------- Comments Moderation Helpers ----------
  const fetchComments = useCallback(async (opts?: Partial<typeof commentFilters>) => {
    if (!isAdmin) return;
    const merged = { ...commentFilters, ...(opts || {}) };
    setCommentFilters(merged);
    setCommentsLoading(true);
    setCommentsError(null);
    try {
      const params = new URLSearchParams();
      params.set('page', String(merged.page));
      params.set('limit', String(merged.limit));
      if (merged.search) params.set('search', merged.search);
      if (merged.postId) params.set('postId', merged.postId);
      if (merged.parentOnly) params.set('parentOnly', 'true');
      const res = await fetch(`/api/admin/comments?${params.toString()}`);
      if (!res.ok) throw new Error(`${res.status}`);
      const json = await res.json();
      setComments(json.comments || []);
      setSelectedCommentIds(new Set());
    } catch {
      setCommentsError('Failed to load comments');
    } finally {
      setCommentsLoading(false);
    }
  }, [commentFilters, isAdmin]);

  const patchComment = useCallback(async (id: string, data: Partial<Pick<AdminComment,'content'>>) => {
    try {
      const res = await fetch(`/api/admin/comments/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!res.ok) throw new Error('Failed');
      await fetchComments();
      setEditingComment(null);
  } catch {
      alert('Failed to update comment');
    }
  }, [fetchComments]);

  const deleteComment = useCallback(async (id: string) => {
    if (!confirm('Delete this comment?')) return;
    try {
      const res = await fetch(`/api/admin/comments/${id}`, { method: 'DELETE' });
      if (!res.ok) {
        const err = await res.json().catch(() => ({}));
        alert(err.error || 'Failed to delete comment');
        return;
      }
      await fetchComments();
  } catch {
      alert('Failed to delete comment');
    }
  }, [fetchComments]);

  // lazy load when switching tabs
  useEffect(() => {
    if (activeTab === 'posts' && posts.length === 0 && !postsLoading) fetchPosts();
    if (activeTab === 'comments' && comments.length === 0 && !commentsLoading) fetchComments();
  }, [activeTab, fetchPosts, fetchComments, posts.length, comments.length, postsLoading, commentsLoading]);

  // Fetch admin data
  useEffect(() => {
    const fetchAdminData = async () => {
      if (!isAdmin) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch stats, users, and communities in parallel
        const [statsResponse, usersResponse, communitiesResponse] = await Promise.all([
          fetch('/api/admin/stats'),
          fetch('/api/admin/users?limit=50'), // Get users with admin privileges
          fetch('/api/communities?limit=50&showArchived=true') // Get all communities for admin view
        ]);

        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats(statsData.stats);
        } else {
          console.error('Failed to fetch stats:', statsResponse.status);
        }

        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setUsers(usersData.users || []);
        } else {
          console.error('Failed to fetch users:', usersResponse.status);
        }

        if (communitiesResponse.ok) {
          const communitiesData = await communitiesResponse.json();
          setCommunities(communitiesData.communities || []);
        } else {
          console.error('Failed to fetch communities:', communitiesResponse.status, await communitiesResponse.text());
        }
      } catch (err) {
        console.error('Error fetching admin data:', err);
        setError('Failed to load admin data');
      } finally {
        setLoading(false);
      }
    };

    fetchAdminData();
  }, [isAdmin]);

  // Refresh data function
  const refreshData = async () => {
    if (!isAdmin) return;
    try {
      setLoading(true);
      const [statsResponse, usersResponse, communitiesResponse] = await Promise.all([
        fetch('/api/admin/stats'),
        fetch('/api/admin/users?limit=50'),
        fetch('/api/communities?limit=50&showArchived=true')
      ]);
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.stats);
      }
      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        setUsers(usersData.users || []);
      }
      if (communitiesResponse.ok) {
        const communitiesData = await communitiesResponse.json();
        setCommunities(communitiesData.communities || []);
      }
    } catch (err) {
      console.error('Error refreshing data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle role change
  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role: newRole }),
      });

      if (response.ok) {
        await response.json(); // body not otherwise used
        // Update local state
        setUsers(users.map(user =>
          user.id === userId ? { ...user, role: newRole, updatedAt: new Date().toISOString() } : user
        ));
        alert('User role updated successfully');
      } else {
        const errorData = await response.json();
        alert(`Failed to update role: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error updating user role:', error);
      alert('Failed to update user role');
    }
  };

  // Handle user deletion
  const handleDeleteUser = async (userId: string, userName: string) => {
    if (!confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Remove user from local state
        setUsers(users.filter(user => user.id !== userId));
        alert('User deleted successfully');
      } else {
        const errorData = await response.json();
        alert(`Failed to delete user: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('Failed to delete user');
    }
  };

  // Handle user edit
  const handleEditUser = (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (user) {
      setEditingUser(user);
    }
  };

  // Handle user update
  interface UserUpdatePayload { name?: string; email?: string; role?: string; bio?: string | null }
  const handleUpdateUser = async (userData: UserUpdatePayload) => {
    if (!editingUser) return;

    try {
      setIsUpdating(true);
      const response = await fetch(`/api/admin/users/${editingUser.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (response.ok) {
        const result = await response.json();
        const updatedUser = result.user as AdminUser;
        setUsers(users.map(u =>
          u.id === editingUser.id ? updatedUser : u
        ));
        setEditingUser(null);
        alert('User updated successfully!');
        // Refresh data to show latest changes
        refreshData();
      } else {
        const errorData = await response.json();
        alert(`Failed to update user: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error updating user:', error);
      alert('Failed to update user');
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle add new user
  interface UserCreatePayload { name: string; email: string; role: string; password?: string }
  const handleAddUser = async (userData: UserCreatePayload) => {
    try {
      setIsUpdating(true);
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create',
          userData: userData,
        }),
      });

      if (response.ok) {
        await response.json();
        // Refresh the users list to include the new user
        refreshData();
        setIsAddingUser(false);
        alert('User created successfully!');
      } else {
        const errorData = await response.json();
        alert(`Failed to create user: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error creating user:', error);
      alert('Failed to create user');
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle user suspend
  const handleSuspendUser = async (userId: string, userName: string) => {
    if (!confirm(`Are you sure you want to suspend user "${userName}"?`)) {
      return;
    }

    try {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'suspend',
          userIds: [userId]
        }),
      });

      if (response.ok) {
        // Update the user in the local state (mark as suspended)
        setUsers(users.map(user =>
          user.id === userId ? { ...user, suspended: true, updatedAt: new Date().toISOString() } : user
        ));
        alert(`User "${userName}" has been suspended successfully!`);
      } else {
        const errorData = await response.json();
        alert(`Failed to suspend user: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error suspending user:', error);
      alert('Failed to suspend user');
    }
  };

  // Handle community edit
  // Handle community edit
  const handleEditCommunity = (community: AdminCommunity) => setEditingCommunity(community);
  // Handle community update
  interface CommunityUpdatePayload { name?: string; description?: string; slug?: string; isPrivate?: boolean; allowPosts?: boolean; requireApproval?: boolean; archived?: boolean; image?: string | null }
  const handleUpdateCommunity = async (communityData: CommunityUpdatePayload) => {
    if (!editingCommunity) return;

    try {
      setIsUpdating(true);
      const response = await fetch(`/api/admin/communities/${editingCommunity.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(communityData),
      });

      if (response.ok) {
        const result = await response.json();
        const updatedCommunity = result.community;
        setCommunities(communities.map(c =>
          c.id === editingCommunity.id ? updatedCommunity : c
        ));
        setEditingCommunity(null);
        alert('Community updated successfully!');
        // Refresh data to show latest changes
        refreshData();
      } else {
        const errorData = await response.json();
        alert(`Failed to update community: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error updating community:', error);
      alert('Failed to update community');
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle community deletion
  const handleDeleteCommunity = async (communityId: string, communityName: string) => {
    if (!confirm(`Are you sure you want to delete community "${communityName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/communities/${communityId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setCommunities(communities.filter(c => c.id !== communityId));
        alert('Community deleted successfully');
      } else {
        const errorData = await response.json();
        alert(`Failed to delete community: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error deleting community:', error);
      alert('Failed to delete community');
    }
  };

  // Handle community image upload
  const handleCommunityImageUpload = async (file: File) => {
    if (!editingCommunity) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size must be less than 5MB');
      return;
    }

    try {
      setIsUploadingImage(true);

      // Upload the image
      const formData = new FormData();
      formData.append('file', file);

      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.error || 'Failed to upload image');
      }

      const uploadResult = await uploadResponse.json();
      const imageUrl = uploadResult.url;

      // Update the community with the new image
      const updateResponse = await fetch(`/api/admin/communities/${editingCommunity.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ image: imageUrl }),
      });

      if (updateResponse.ok) {
        const result = await updateResponse.json();
        const updatedCommunity = result.community;

        // Update local state
        setCommunities(communities.map(c =>
          c.id === editingCommunity.id ? updatedCommunity : c
        ));
        setEditingCommunity({ ...editingCommunity, image: imageUrl });

        // Show success message with better UX
        const successDiv = document.createElement('div');
        successDiv.className = 'toast toast-top toast-end';
        successDiv.innerHTML = `
          <div class="alert alert-success">
            <svg class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>Community image updated successfully!</span>
          </div>
        `;
        document.body.appendChild(successDiv);
        setTimeout(() => successDiv.remove(), 3000);
      } else {
        const errorData = await updateResponse.json();
        alert(`Failed to update community image: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error uploading community image:', error);
      alert('Failed to upload community image');
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Check if user is admin
  if (status === "loading") {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (!session?.user || (session.user as { role?: string })?.role !== "ADMIN") {
    return (
      <div className="text-center py-12">
        <div className="alert alert-error max-w-md mx-auto">
          <svg className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Access denied. Admin privileges required.</span>
        </div>
        <Link href="/" className="btn btn-primary mt-4">
          Go Home
        </Link>
      </div>
    );
  }

  // utilities moved to lib/adminUtils



  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <p className="text-base-content/70">Manage your forum and users</p>
      </div>

      {/* Tab Bar */}
      <div className="tabs tabs-boxed flex flex-wrap gap-2">
        <button className={`tab ${activeTab === 'overview' ? 'tab-active' : ''}`} onClick={() => setActiveTab('overview')}>Overview</button>
        <button className={`tab ${activeTab === 'users' ? 'tab-active' : ''}`} onClick={() => setActiveTab('users')}>Users</button>
        <button className={`tab ${activeTab === 'posts' ? 'tab-active' : ''}`} onClick={() => setActiveTab('posts')}>Posts</button>
        <button className={`tab ${activeTab === 'comments' ? 'tab-active' : ''}`} onClick={() => setActiveTab('comments')}>Comments</button>
        <button className={`tab ${activeTab === 'reports' ? 'tab-active' : ''}`} onClick={() => setActiveTab('reports')}>Reports</button>
        <button className={`tab ${activeTab === 'analytics' ? 'tab-active' : ''}`} onClick={() => setActiveTab('analytics')}>Analytics</button>
        <button className={`tab ${activeTab === 'settings' ? 'tab-active' : ''}`} onClick={() => setActiveTab('settings')}>Settings</button>
      </div>

      {/* Comments Moderation Tab */}
      {activeTab === 'comments' && (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body space-y-4">
            <h2 className="card-title">Comments & Replies</h2>
            <p className="text-sm opacity-70">Search, edit or delete comments and replies. Top-level comments with replies must have replies deleted first.</p>
            <div className="flex flex-col lg:flex-row gap-2 lg:items-center">
              <div className="flex gap-2">
                <input type="text" placeholder="Search content" className="input input-bordered input-sm w-56" defaultValue={commentFilters.search || ''} onKeyDown={(e) => { if (e.key === 'Enter') fetchComments({ page: 1, search: (e.target as HTMLInputElement).value || undefined }); }} />
                <label className="label cursor-pointer gap-2">
                  <span className="text-xs">Parents only</span>
                  <input type="checkbox" className="toggle toggle-xs" checked={commentFilters.parentOnly} onChange={(e) => fetchComments({ page: 1, parentOnly: e.target.checked })} />
                </label>
              </div>
              <div className="flex gap-2 ml-auto">
                <button className="btn btn-sm rounded-lg" onClick={() => fetchComments()}>Refresh</button>
                <button className="btn btn-sm btn-error rounded-lg" disabled={!selectedCommentIds.size} onClick={async () => {
                  if (!confirm(`Delete ${selectedCommentIds.size} comments?`)) return;
                  for (const id of selectedCommentIds) { // sequential awaits fine here; small batch sizes expected
                    await deleteComment(id);
                  }
                }}>Delete Selected</button>
              </div>
            </div>
            {commentsError && <div className="alert alert-error py-2"><span className="text-sm">{commentsError}</span></div>}
            <div className="overflow-x-auto border rounded-lg">
              <table className="table table-xs">
                <thead>
                  <tr>
                    <th><input type="checkbox" className="checkbox checkbox-xs" onChange={(e) => { if (e.target.checked) setSelectedCommentIds(new Set(comments.map(c => c.id))); else setSelectedCommentIds(new Set()); }} checked={comments.length>0 && selectedCommentIds.size===comments.length} /></th>
                    <th>Excerpt</th>
                    <th>Author</th>
                    <th>Post</th>
                    <th>Replies</th>
                    <th>Likes</th>
                    <th>Created</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {commentsLoading && <tr><td colSpan={8} className="py-6 text-center"><span className="loading loading-spinner" /></td></tr>}
                  {!commentsLoading && comments.length === 0 && <tr><td colSpan={8} className="py-6 text-center text-base-content/60">No comments</td></tr>}
                  {!commentsLoading && comments.map(c => (
                    <tr key={c.id} className={selectedCommentIds.has(c.id) ? 'active' : ''}>
                      <td><input type="checkbox" className="checkbox checkbox-xs" checked={selectedCommentIds.has(c.id)} onChange={(e) => { const next = new Set(selectedCommentIds); if (e.target.checked) next.add(c.id); else next.delete(c.id); setSelectedCommentIds(next); }} /></td>
                      <td className="max-w-[220px] truncate" title={c.content}>{c.parentId && <span className="badge badge-ghost badge-xs mr-1">Reply</span>}{c.content}</td>
                      <td className="text-xs">{c.author?.name || '—'}</td>
                      <td className="text-xs" title={c.post?.title}>{c.post?.title?.slice(0,20) || '—'}</td>
                      <td className="text-center text-xs">{c._count?.replies ?? 0}</td>
                      <td className="text-center text-xs">{c._count?.likes ?? 0}</td>
                      <td className="text-[10px]">{new Date(c.createdAt).toLocaleDateString()}</td>
                      <td>
                        <div className="dropdown dropdown-end">
                          <div tabIndex={0} role="button" className="btn btn-ghost btn-xs">⋮</div>
                          <ul tabIndex={0} className="dropdown-content menu p-1 shadow bg-base-100 rounded-box w-28">
                            <li><a onClick={() => setEditingComment(c)}>Edit</a></li>
                            <li><a className="text-error" onClick={() => deleteComment(c.id)}>Delete</a></li>
                          </ul>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="flex justify-between items-center text-xs pt-1">
              <div>Page {commentFilters.page}</div>
              <div className="flex gap-2">
                <button className="btn btn-xs rounded-lg" disabled={commentFilters.page<=1 || commentsLoading} onClick={() => fetchComments({ page: commentFilters.page - 1 })}>Prev</button>
                <button className="btn btn-xs rounded-lg" disabled={comments.length < commentFilters.limit || commentsLoading} onClick={() => fetchComments({ page: commentFilters.page + 1 })}>Next</button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Overview Tab */}
      {activeTab === "overview" && (
        <div className="space-y-6">
          {loading && (
            <div className="flex justify-center items-center py-12">
              <div className="loading loading-spinner loading-lg"></div>
            </div>
          )}

          {error && (
            <div className="alert alert-error">
              <svg className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error}</span>
            </div>
          )}

          {!loading && !error && (
            <>
              {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="stat bg-base-100 shadow rounded-lg">
              <div className="stat-figure text-primary">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <div className="stat-title">Total Users</div>
              <div className="stat-value text-primary">{stats?.totalUsers || 0}</div>
              <div className="stat-desc">+{stats?.newUsersToday || 0} today</div>
            </div>

            <div className="stat bg-base-100 shadow rounded-lg">
              <div className="stat-figure text-secondary">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="stat-title">Total Posts</div>
              <div className="stat-value text-secondary">{stats?.totalPosts || 0}</div>
              <div className="stat-desc">+{stats?.newPostsToday || 0} today</div>
            </div>

            <div className="stat bg-base-100 shadow rounded-lg">
              <div className="stat-figure text-accent">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="stat-title">Total Comments</div>
              <div className="stat-value text-accent">{stats?.totalComments || 0}</div>
              <div className="stat-desc">+{stats?.newCommentsToday || 0} today</div>
            </div>

            <div className="stat bg-base-100 shadow rounded-lg">
              <div className="stat-figure text-error">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
              </div>
              <div className="stat-title">Total Likes</div>
              <div className="stat-value text-error">{stats?.totalLikes || 0}</div>
              <div className="stat-desc">Engagement metric</div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h2 className="card-title">Recent Posts</h2>
                <div className="space-y-3">
                  {stats?.recentPosts?.length ? stats.recentPosts.map((post) => (
                    <div key={post.id} className="flex items-center justify-between p-3 bg-base-200 rounded-lg">
                      <div>
                        <h3 className="font-medium text-sm">{post.title}</h3>
                        <p className="text-xs text-base-content/70">
                          by {post.author} • {formatTimeAgo(new Date(post.createdAt))}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`badge badge-xs ${post.published ? 'badge-success' : 'badge-warning'}`}>
                          {post.published ? 'published' : 'draft'}
                        </span>
                        <span className="text-xs">{post.likes}♥ {post.comments}💬</span>
                      </div>
                    </div>
                  )) : (
                    <div className="text-center text-base-content/50 py-4">
                      No recent posts
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="card bg-base-100 shadow-xl">
              <div className="card-body">
                <h2 className="card-title">Quick Actions</h2>
                <div className="space-y-3">
                  <Link href="/posts/create" className="btn btn-primary btn-sm w-full justify-start">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Create New Post
                  </Link>
                  <button
                    className="btn btn-secondary btn-sm w-full justify-start"
                    onClick={() => setActiveTab("users")}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    Manage Users
                  </button>
                  <button
                    className="btn btn-accent btn-sm w-full justify-start"
                    onClick={() => setActiveTab("analytics")}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 012 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    View Analytics
                  </button>
                  <button
                    className="btn btn-warning btn-sm w-full justify-start"
                    onClick={() => setActiveTab("settings")}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Forum Settings
                  </button>
                  <button
                    className="btn btn-info btn-sm w-full justify-start"
                    onClick={() => setActiveTab("posts")}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Manage Posts
                  </button>
                  <button
                    className="btn btn-info btn-sm w-full justify-start"
                    onClick={() => setActiveTab("communities")}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    Manage Communities
                  </button>
                  <button
                    className="btn btn-error btn-sm w-full justify-start"
                    onClick={() => setActiveTab("reports")}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    View Reports
                  </button>
                </div>
              </div>
            </div>
          </div>
            </>
          )}
        </div>
      )}

      {/* Users Tab */}
      {activeTab === "users" && (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex justify-between items-center mb-4">
              <h2 className="card-title">User Management</h2>
              <button
                className="btn btn-primary btn-sm rounded-lg"
                onClick={() => setIsAddingUser(true)}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Add User
              </button>
            </div>
            
            <div className="overflow-x-auto">
              <table className="table table-zebra">
                <thead>
                  <tr>
                    <th>User</th>
                    <th>Role</th>
                    <th>Joined</th>
                    <th>Last Active</th>
                    <th>Posts</th>
                    <th>Comments</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.length ? users.map((user) => (
                    <tr key={user.id}>
                      <td>
                        <div className="flex items-center space-x-3">
                          <div className="avatar placeholder">
                            <div className="bg-neutral text-neutral-content rounded-full w-8">
                              <span className="text-xs">{user.name?.[0] || '?'}</span>
                            </div>
                          </div>
                          <div>
                            <div className="font-bold text-sm">{user.name || 'Unknown'}</div>
                            <div className="text-xs opacity-50">{user.email}</div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <select
                          className={`select select-xs ${getRoleBadgeColor(user.role)}`}
                          value={user.role}
                          onChange={(e) => handleRoleChange(user.id, e.target.value)}
                        >
                          <option value="USER">USER</option>
                          <option value="EDITOR">EDITOR</option>
                          <option value="ADMIN">ADMIN</option>
                        </select>
                      </td>
                      <td className="text-xs">{formatTimeAgo(new Date(user.createdAt))}</td>
                      <td className="text-xs">{formatTimeAgo(new Date(user.updatedAt))}</td>
                      <td>{user._count.posts}</td>
                      <td>{user._count.comments}</td>
                      <td>
                        <div className="dropdown dropdown-end">
                          <div tabIndex={0} role="button" className="btn btn-ghost btn-xs">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                            </svg>
                          </div>
                          <ul tabIndex={0} className="dropdown-content z-[50] menu p-2 shadow bg-base-100 rounded-box w-32">
                            <li><a className="text-xs" onClick={() => handleEditUser(user.id)}>Edit</a></li>
                            <li><a className="text-xs" onClick={() => handleSuspendUser(user.id, user.name || 'Unknown')}>Suspend</a></li>
                            <li><a className="text-xs text-error" onClick={() => handleDeleteUser(user.id, user.name || 'Unknown')}>Delete</a></li>
                          </ul>
                        </div>
                      </td>
                    </tr>
                  )) : (
                    <tr>
                      <td colSpan={7} className="text-center text-base-content/50 py-8">
                        {loading ? 'Loading users...' : 'No users found'}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Communities Tab */}
      {activeTab === "communities" && (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex justify-between items-center mb-4">
              <h2 className="card-title">Community Management</h2>
              <div className="flex gap-2">
                <Link href="/communities/create" className="btn btn-primary btn-sm">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  New Community
                </Link>
                <button
                  className="btn btn-secondary btn-sm rounded-lg"
                  onClick={refreshData}
                  disabled={loading}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  {loading ? 'Refreshing...' : 'Refresh'}
                </button>
                <button className="btn btn-outline btn-sm rounded-lg">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Export
                </button>
              </div>
            </div>

            {/* Community Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="stat bg-base-200 rounded-lg">
                <div className="stat-title">Total Communities</div>
                <div className="stat-value text-primary">{communities.length}</div>
                <div className="stat-desc">All time</div>
              </div>
              <div className="stat bg-base-200 rounded-lg">
                <div className="stat-title">Active</div>
                <div className="stat-value text-success">{communities.filter(c => c.archived !== true).length}</div>
                <div className="stat-desc">Public communities</div>
              </div>
              <div className="stat bg-base-200 rounded-lg">
                <div className="stat-title">Private</div>
                <div className="stat-value text-warning">{communities.filter(c => c.isPrivate === true).length}</div>
                <div className="stat-desc">Private communities</div>
              </div>
              <div className="stat bg-base-200 rounded-lg">
                <div className="stat-title">Archived</div>
                <div className="stat-value text-error">{communities.filter(c => c.archived === true).length}</div>
                <div className="stat-desc">Deactivated</div>
              </div>
            </div>

            {/* Community Management Interface */}
            <div className="overflow-x-auto">
              <table className="table table-zebra">
                <thead>
                  <tr>
                    <th>
                      <input type="checkbox" className="checkbox checkbox-sm" />
                    </th>
                    <th>Community</th>
                    <th>Creator</th>
                    <th>Members</th>
                    <th>Posts</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <div className="loading loading-spinner loading-lg"></div>
                      </td>
                    </tr>
                  ) : communities.length > 0 ? (
                    communities.map((community) => (
                      <tr key={community.id}>
                        <td>
                          <input type="checkbox" className="checkbox checkbox-sm" />
                        </td>
                        <td>
                          <div className="flex items-center space-x-3">
                            <div className="avatar">
                              <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-primary/30 to-secondary/30 flex items-center justify-center">
                                {community.image ? (
                                  <Image
                                    src={community.image}
                                    alt={community.name}
                                    width={48}
                                    height={48}
                                    className="w-full h-full object-cover rounded-lg"
                                  />
                                ) : (
                                  <span className="text-xl">🏘️</span>
                                )}
                              </div>
                            </div>
                            <div>
                              <div className="font-bold text-sm">{community.name}</div>
                              <div className="text-xs opacity-50">{community.description?.substring(0, 50)}...</div>
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="flex items-center space-x-2">
                            <div className="avatar placeholder">
                              <div className="bg-neutral text-neutral-content rounded-full w-6">
                                <span className="text-xs">{community.creator?.name?.[0] || '?'}</span>
                              </div>
                            </div>
                            <span className="text-sm">{community.creator?.name || 'Unknown'}</span>
                          </div>
                        </td>
                        <td>{community._count?.members || 0}</td>
                        <td>{community._count?.posts || 0}</td>
                        <td>
                          <span className={`badge badge-sm ${community.archived === true ? 'badge-warning' : community.isPrivate ? 'badge-info' : 'badge-success'}`}>
                            {community.archived === true ? 'Archived' : community.isPrivate ? 'Private' : 'Active'}
                          </span>
                        </td>
                        <td className="text-xs">{formatTimeAgo(new Date(community.createdAt))}</td>
                        <td>
                          <div className="dropdown dropdown-end">
                            <div tabIndex={0} role="button" className="btn btn-ghost btn-xs">
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                              </svg>
                            </div>
                            <ul tabIndex={0} className="dropdown-content z-[50] menu p-2 shadow bg-base-100 rounded-box w-40">
                              <li><a className="text-xs" onClick={() => handleEditCommunity(community)}>Edit Details</a></li>
                              <li><a className="text-xs" onClick={() => alert('Change picture functionality will open image upload modal')}>Change Picture</a></li>
                              <li><a className="text-xs" onClick={() => alert('Member management will show community members and roles')}>Manage Members</a></li>
                              <li><Link href={`/communities/${community.slug}`} className="text-xs">View Community</Link></li>
                              <li><a className="text-xs" onClick={() => alert('Archive functionality will deactivate the community')}>Archive</a></li>
                              <li><a className="text-xs text-error" onClick={() => handleDeleteCommunity(community.id, community.name)}>Delete</a></li>
                            </ul>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="text-center py-8">
                        <div className="text-base-content/50">
                          <p className="mb-2">No communities found</p>
                          <Link href="/communities/create" className="btn btn-primary btn-sm">
                            Create First Community
                          </Link>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Community Management Info */}
            <div className="alert alert-success mt-6">
              <svg className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span>Community management system is fully operational! Showing {communities.length} communities with real-time data from the database. All management features are ready including image uploads, member management, and content moderation.</span>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-2 mt-4">
              <Link href="/communities" className="btn btn-outline btn-sm">View All Communities</Link>
              <button
                className="btn btn-outline btn-sm"
                onClick={() => alert('Private communities management will show invitation-only communities')}
              >
                Private Communities
              </button>
              <button
                className="btn btn-outline btn-sm"
                onClick={() => alert('Archived communities will show deactivated communities')}
              >
                Archived Communities
              </button>
              <button
                className="btn btn-outline btn-sm"
                onClick={() => alert('Community analytics will show growth and engagement metrics')}
              >
                Community Analytics
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Posts Tab */}
      {activeTab === "posts" && (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex flex-col lg:flex-row lg:items-center gap-2 mb-4">
              <h2 className="card-title">Post Management</h2>
              <div className="flex gap-2 ml-auto">
                <input type="text" placeholder="Search title/content" className="input input-bordered input-sm w-60" defaultValue={postFilters.search || ''} onKeyDown={(e) => { if (e.key === 'Enter') fetchPosts({ page:1, search: (e.target as HTMLInputElement).value || undefined }); }} />
                <button className="btn btn-sm" onClick={() => fetchPosts()}>Refresh</button>
                <button className="btn btn-sm btn-error" disabled={!selectedPostIds.size} onClick={async () => {
                  if (!confirm(`Delete ${selectedPostIds.size} posts?`)) return;
                  for (const id of selectedPostIds) { // sequential
                     
                    await deletePost(id);
                  }
                }}>Delete Selected</button>
              </div>
            </div>

            {/* Post Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="stat bg-base-200 rounded-lg">
                <div className="stat-title">Total Posts</div>
                <div className="stat-value text-primary">{stats?.totalPosts || 0}</div>
                <div className="stat-desc">All time</div>
              </div>
              <div className="stat bg-base-200 rounded-lg">
                <div className="stat-title">Published</div>
                <div className="stat-value text-success">{stats?.totalPosts || 0}</div>
                <div className="stat-desc">Live posts</div>
              </div>
              <div className="stat bg-base-200 rounded-lg">
                <div className="stat-title">Drafts</div>
                <div className="stat-value text-warning">0</div>
                <div className="stat-desc">Unpublished</div>
              </div>
              <div className="stat bg-base-200 rounded-lg">
                <div className="stat-title">Today</div>
                <div className="stat-value text-accent">{stats?.newPostsToday || 0}</div>
                <div className="stat-desc">New posts</div>
              </div>
            </div>

            {/* Post Management Interface */}
            {postsError && <div className="alert alert-error mb-4 py-2"><span className="text-sm">{postsError}</span></div>}
            <div className="overflow-x-auto border rounded-lg mb-4">
              <table className="table table-xs">
                <thead>
                  <tr>
                    <th><input type="checkbox" className="checkbox checkbox-xs" onChange={(e) => { if (e.target.checked) setSelectedPostIds(new Set(posts.map(p=>p.id))); else setSelectedPostIds(new Set()); }} checked={posts.length>0 && selectedPostIds.size===posts.length} /></th>
                    <th>Title</th>
                    <th>Author</th>
                    <th>Comments</th>
                    <th>Likes</th>
                    <th>Status</th>
                    <th>Flags</th>
                    <th>Created</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {postsLoading && <tr><td colSpan={9} className="py-6 text-center"><span className="loading loading-spinner"/></td></tr>}
                  {!postsLoading && posts.length===0 && <tr><td colSpan={9} className="py-6 text-center text-base-content/60">No posts</td></tr>}
                  {!postsLoading && posts.map(p => (
                    <tr key={p.id} className={selectedPostIds.has(p.id)?'active':''}>
                      <td><input type="checkbox" className="checkbox checkbox-xs" checked={selectedPostIds.has(p.id)} onChange={(e) => { const next = new Set(selectedPostIds); if (e.target.checked) next.add(p.id); else next.delete(p.id); setSelectedPostIds(next); }} /></td>
                      <td className="max-w-[220px] truncate" title={p.title}>{p.title}</td>
                      <td className="text-xs" title={p.author?.email}>{p.author?.name || '—'}</td>
                      <td className="text-center text-xs">{p._count?.comments ?? 0}</td>
                      <td className="text-center text-xs">{p._count?.likes ?? 0}</td>
                      <td><span className={`badge badge-ghost badge-xs ${p.published ? 'badge-success' : 'badge-warning'}`}>{p.published ? 'Published' : 'Draft'}</span></td>
                      <td className="text-[10px] space-x-1">{p.featured && <span className="badge badge-info badge-outline">feat</span>}{p.pinned && <span className="badge badge-secondary badge-outline">pin</span>}{p.locked && <span className="badge badge-error badge-outline">lock</span>}</td>
                      <td className="text-[10px]">{new Date(p.createdAt).toLocaleDateString()}</td>
                      <td>
                        <div className="dropdown dropdown-end">
                          <div tabIndex={0} role="button" className="btn btn-ghost btn-xs">⋮</div>
                          <ul tabIndex={0} className="dropdown-content menu p-1 shadow bg-base-100 rounded-box w-32">
                            <li><a onClick={() => setEditingPost(p)}>Edit</a></li>
                            <li><a className="text-error" onClick={() => deletePost(p.id)}>Delete</a></li>
                          </ul>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="flex justify-between items-center text-xs mb-4">
              <div>Page {postFilters.page}</div>
              <div className="flex gap-2">
                <button className="btn btn-xs" disabled={postFilters.page<=1 || postsLoading} onClick={() => fetchPosts({ page: postFilters.page - 1 })}>Prev</button>
                <button className="btn btn-xs" disabled={posts.length < postFilters.limit || postsLoading} onClick={() => fetchPosts({ page: postFilters.page + 1 })}>Next</button>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-2">
              <Link href="/posts" className="btn btn-outline btn-sm">View All Posts</Link>
              <button
                className="btn btn-outline btn-sm"
                onClick={() => alert('Pending review functionality will show posts awaiting moderation')}
              >
                Pending Review
              </button>
              <button
                className="btn btn-outline btn-sm"
                onClick={() => setActiveTab("reports")}
              >
                Reported Posts
              </button>
              <button
                className="btn btn-outline btn-sm"
                onClick={() => alert('Featured posts management will allow promoting important content')}
              >
                Featured Posts
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Reports Tab */}
      {activeTab === "reports" && (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex flex-col gap-4">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
                <h2 className="card-title">Reports Management</h2>
                <div className="flex flex-wrap gap-2">
                  <select className="select select-bordered select-sm" value={reportFilters.status || ''} onChange={(e) => fetchReports({ page: 1, status: e.target.value || undefined })}>
                    <option value="">All Statuses</option>
                    <option value="PENDING">Pending</option>
                    <option value="REVIEWED">Reviewed</option>
                    <option value="RESOLVED">Resolved</option>
                    <option value="DISMISSED">Dismissed</option>
                  </select>
                  <select className="select select-bordered select-sm" value={reportFilters.type || ''} onChange={(e) => fetchReports({ page: 1, type: e.target.value || undefined })}>
                    <option value="">All Types</option>
                    <option value="POST">Post</option>
                    <option value="COMMENT">Comment</option>
                    <option value="USER">User</option>
                  </select>
                  <button className="btn btn-sm" disabled={!selectedReportIds.size} onClick={() => updateReportStatus(Array.from(selectedReportIds), 'REVIEWED')}>Mark Reviewed</button>
                  <button className="btn btn-sm btn-success" disabled={!selectedReportIds.size} onClick={() => updateReportStatus(Array.from(selectedReportIds), 'RESOLVED')}>Resolve</button>
                  <button className="btn btn-sm btn-warning" disabled={!selectedReportIds.size} onClick={() => updateReportStatus(Array.from(selectedReportIds), 'DISMISSED')}>Dismiss</button>
                  <button className="btn btn-sm btn-outline" onClick={() => fetchReports()}>Refresh</button>
                </div>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-2 md:grid-cols-6 gap-3">
                <div className="stat bg-base-200 rounded">
                  <div className="stat-title">Total</div>
                  <div className="stat-value text-primary text-lg">{reportStats?.totalReports ?? (reportsLoading ? '…' : 0)}</div>
                </div>
                <div className="stat bg-base-200 rounded">
                  <div className="stat-title">Pending</div>
                  <div className="stat-value text-warning text-lg">{reportStats?.pendingReports ?? (reportsLoading ? '…' : 0)}</div>
                </div>
                <div className="stat bg-base-200 rounded">
                  <div className="stat-title">Reviewed</div>
                  <div className="stat-value text-info text-lg">{reportStats?.reviewedReports ?? (reportsLoading ? '…' : 0)}</div>
                </div>
                <div className="stat bg-base-200 rounded">
                  <div className="stat-title">Resolved</div>
                  <div className="stat-value text-success text-lg">{reportStats?.resolvedReports ?? (reportsLoading ? '…' : 0)}</div>
                </div>
                <div className="stat bg-base-200 rounded">
                  <div className="stat-title">Dismissed</div>
                  <div className="stat-value text-neutral text-lg">{reportStats?.dismissedReports ?? (reportsLoading ? '…' : 0)}</div>
                </div>
                <div className="stat bg-base-200 rounded">
                  <div className="stat-title">Today</div>
                  <div className="stat-value text-accent text-lg">{reportStats?.reportsToday ?? (reportsLoading ? '…' : 0)}</div>
                </div>
              </div>

              {reportsError && <div className="alert alert-error"><span>{reportsError}</span></div>}

              {/* Reports Table */}
              <div className="overflow-x-auto">
                <table className="table table-sm">
                  <thead>
                    <tr>
                      <th><input type="checkbox" className="checkbox checkbox-xs" onChange={(e) => {
                        if (e.target.checked) setSelectedReportIds(new Set(reports.map(r => r.id)));
                        else setSelectedReportIds(new Set());
                      }} checked={reports.length > 0 && selectedReportIds.size === reports.length} /></th>
                      <th>Type</th>
                      <th>Reason</th>
                      <th>Target</th>
                      <th>Reporter</th>
                      <th>Status</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportsLoading && (
                      <tr><td colSpan={8} className="text-center py-6"><span className="loading loading-spinner"></span></td></tr>
                    )}
                    {!reportsLoading && reports.length === 0 && (
                      <tr><td colSpan={8} className="text-center py-6 text-base-content/60">No reports found</td></tr>
                    )}
                    {!reportsLoading && reports.map(r => (
                      <tr key={r.id} className={selectedReportIds.has(r.id) ? 'active' : ''}>
                        <td><input type="checkbox" className="checkbox checkbox-xs" checked={selectedReportIds.has(r.id)} onChange={(e) => {
                          const next = new Set(selectedReportIds);
                          if (e.target.checked) next.add(r.id); else next.delete(r.id);
                          setSelectedReportIds(next);
                        }}/></td>
                        <td className="text-xs font-mono">{r.type}</td>
                        <td className="text-xs max-w-[140px] truncate" title={r.reason}>{r.reason}</td>
                        <td className="text-xs">
                          {r.post ? <span title={r.post.title}>Post</span> : r.comment ? 'Comment' : r.user ? 'User' : '—'}
                        </td>
                        <td className="text-xs">{r.reporter?.name || 'Anon'}</td>
                        <td>
                          <select className="select select-xs" value={r.status} onChange={(e) => updateReportStatus([r.id], e.target.value)}>
                            <option>PENDING</option>
                            <option>REVIEWED</option>
                            <option>RESOLVED</option>
                            <option>DISMISSED</option>
                          </select>
                        </td>
                        <td className="text-xs">{new Date(r.createdAt).toLocaleDateString()}</td>
                        <td>
                          <button className="btn btn-ghost btn-xs" onClick={() => updateReportStatus([r.id], 'REVIEWED')}>Review</button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-2">
                <div className="text-xs opacity-70">Page {reportFilters.page}</div>
                <div className="flex gap-2">
                  <button className="btn btn-xs" disabled={reportFilters.page <= 1 || reportsLoading} onClick={() => fetchReports({ page: reportFilters.page - 1 })}>Prev</button>
                  <button className="btn btn-xs" disabled={reports.length < reportFilters.limit || reportsLoading} onClick={() => fetchReports({ page: reportFilters.page + 1 })}>Next</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === "analytics" && (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
              <h2 className="card-title">Advanced Analytics</h2>
              <div className="flex gap-2 items-center">
                <select className="select select-bordered select-sm" value={analyticsPeriod} onChange={(e) => fetchAnalytics(parseInt(e.target.value))}>
                  <option value={7}>Last 7 days</option>
                  <option value={30}>Last 30 days</option>
                  <option value={90}>Last 90 days</option>
                </select>
                <button className="btn btn-sm" onClick={() => fetchAnalytics()}>Refresh</button>
              </div>
            </div>
            {analyticsError && <div className="alert alert-error mb-4"><span>{analyticsError}</span></div>}
            {analyticsLoading && <div className="flex justify-center py-10"><span className="loading loading-spinner loading-lg" /></div>}
            {!analyticsLoading && analytics && (
              <>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3 mb-6">
                  <div className="stat bg-base-200 rounded"><div className="stat-title">Users</div><div className="stat-value text-primary text-lg">{analytics.overview.totalUsers}</div></div>
                  <div className="stat bg-base-200 rounded"><div className="stat-title">Active</div><div className="stat-value text-success text-lg">{analytics.overview.activeUsers}</div></div>
                  <div className="stat bg-base-200 rounded"><div className="stat-title">Posts</div><div className="stat-value text-secondary text-lg">{analytics.overview.totalPosts}</div></div>
                  <div className="stat bg-base-200 rounded"><div className="stat-title">Comments</div><div className="stat-value text-accent text-lg">{analytics.overview.totalComments}</div></div>
                  <div className="stat bg-base-200 rounded"><div className="stat-title">Likes</div><div className="stat-value text-error text-lg">{analytics.overview.totalLikes}</div></div>
                  <div className="stat bg-base-200 rounded"><div className="stat-title">Engagement %</div><div className="stat-value text-info text-lg">{analytics.overview.engagementRate}</div></div>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  <div className="card bg-base-200"><div className="card-body p-4">
                    <h3 className="font-semibold text-sm mb-2">User Growth (daily)</h3>
                    <div className="flex gap-1 items-end h-32">
                      {analytics.charts.userGrowth.map((p: AnalyticsPoint) => (
                        <div key={p.date} className="bg-primary/70 hover:bg-primary transition-colors" style={{ height: `${Math.min(100, (p.users || 0) * 12)}%`, width: '6px' }} title={`${p.date}: ${p.users}`} />
                      ))}
                    </div>
                  </div></div>
                  <div className="card bg-base-200"><div className="card-body p-4">
                    <h3 className="font-semibold text-sm mb-2">Engagement (last 7d)</h3>
                    <div className="flex gap-1 items-end h-32">
                      {analytics.charts.dailyEngagement.map((p: AnalyticsPoint) => (
                        <div key={p.date} className="bg-secondary/70 hover:bg-secondary transition-colors" style={{ height: `${Math.min(100, (p.engagement || 0) * 4)}%`, width: '8px' }} title={`${p.date}: ${p.engagement}`} />
                      ))}
                    </div>
                  </div></div>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="card bg-base-200"><div className="card-body p-4">
                    <h3 className="font-semibold text-sm mb-2">Top Users</h3>
                    <ul className="space-y-1 text-xs max-h-48 overflow-y-auto">
                      {analytics.topUsers.map((u: TopUser) => (
                        <li key={u.id} className="flex justify-between"><span className="truncate max-w-[140px]" title={u.name}>{u.name || 'Unknown'}</span><span>{u.totalActivity}</span></li>
                      ))}
                    </ul>
                  </div></div>
                  <div className="card bg-base-200"><div className="card-body p-4">
                    <h3 className="font-semibold text-sm mb-2">Top Communities</h3>
                    <ul className="space-y-1 text-xs max-h-48 overflow-y-auto">
                      {analytics.topCommunities.map((c: TopCommunity) => (
                        <li key={c.id} className="flex justify-between"><span className="truncate max-w-[140px]" title={c.name}>{c.name}</span><span>{c.activity}</span></li>
                      ))}
                    </ul>
                  </div></div>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === "settings" && (
        <div className="space-y-6">
          <div className="card bg-base-100 shadow-xl">
            <div className="card-body">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
                <h2 className="card-title">Forum Settings</h2>
                <div className="flex gap-2">
                  <button className="btn btn-primary btn-sm" disabled={!Object.keys(settingsDirty).length || settingsLoading} onClick={saveDirtySettings}>
                    {settingsLoading ? <span className="loading loading-spinner loading-xs" /> : 'Save Changes'}
                  </button>
                  <button className="btn btn-secondary btn-sm" disabled={settingsLoading} onClick={() => fetchSettings()}>Reload</button>
                </div>
              </div>
              {settingsError && <div className="alert alert-error mb-4"><span>{settingsError}</span></div>}
              {settingsLoading && settings.length === 0 && <div className="flex justify-center py-10"><span className="loading loading-spinner loading-lg"/></div>}
              {!settingsLoading && settings.length > 0 && (
                               <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {Object.entries(groupedSettings).map(([category, items]) => (
                    <div key={category} className="card bg-base-200">
                      <div className="card-body p-4">
                        <h3 className="font-semibold mb-2 capitalize">{category}</h3>
                        <div className="space-y-4">
                          {items.map(item => {
                            const current = settingsDirty[item.key] ?? item.value;
                            const markDirty = (val: string) => setSettingsDirty(d => ({ ...d, [item.key]: val }));
                            return (
                              <div key={item.id} className="form-control">
                                <label className="label"><span className="label-text text-xs font-medium">{item.key}</span></label>
                                {item.type === 'BOOLEAN' ? (
                                  <label className="cursor-pointer label justify-start gap-3 p-0">
                                    <input type="checkbox" className="toggle toggle-primary toggle-sm" checked={current === 'true'} onChange={(e) => markDirty(e.target.checked ? 'true' : 'false')} />
                                    <span className="text-xs opacity-70">{item.description}</span>
                                  </label>
                                ) : item.type === 'NUMBER' ? (
                                  <input type="number" className="input input-bordered input-sm" value={current} onChange={(e) => markDirty(e.target.value)} />
                                ) : item.type === 'JSON' ? (
                                  <textarea className="textarea textarea-bordered textarea-sm font-mono" rows={3} value={current} onChange={(e) => markDirty(e.target.value)} />
                                ) : (
                                  <input type="text" className="input input-bordered input-sm" value={current} onChange={(e) => markDirty(e.target.value)} />
                                )}
                                {settingsDirty[item.key] !== undefined && settingsDirty[item.key] !== item.value && (
                                  <span className="text-[10px] text-warning mt-1">modified</span>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {/* Edit Post Modal */}
      {editingPost && (
        <div className="fixed inset-0 bg-black/40 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="card bg-base-100 w-full max-w-xl">
            <div className="card-body space-y-4">
              <h3 className="card-title text-sm">Edit Post</h3>
              <input className="input input-bordered input-sm" value={editingPost.title} onChange={(e) => setEditingPost({ ...editingPost, title: e.target.value })} />
              <textarea className="textarea textarea-bordered textarea-sm h-40" value={editingPost.content} onChange={(e) => setEditingPost({ ...editingPost, content: e.target.value })} />
              <div className="flex flex-wrap gap-4 text-xs">
                <label className="flex items-center gap-2"><input type="checkbox" className="toggle toggle-xs" checked={editingPost.published} onChange={(e) => setEditingPost({ ...editingPost, published: e.target.checked })} /> <span>Published</span></label>
                <label className="flex items-center gap-2"><input type="checkbox" className="toggle toggle-xs" checked={!!editingPost.featured} onChange={(e) => setEditingPost({ ...editingPost, featured: e.target.checked })} /> <span>Featured</span></label>
                <label className="flex items-center gap-2"><input type="checkbox" className="toggle toggle-xs" checked={!!editingPost.pinned} onChange={(e) => setEditingPost({ ...editingPost, pinned: e.target.checked })} /> <span>Pinned</span></label>
                <label className="flex items-center gap-2"><input type="checkbox" className="toggle toggle-xs" checked={!!editingPost.locked} onChange={(e) => setEditingPost({ ...editingPost, locked: e.target.checked })} /> <span>Locked</span></label>
              </div>
              <div className="flex justify-end gap-2">
                <button className="btn btn-sm" onClick={() => setEditingPost(null)}>Cancel</button>
                <button className="btn btn-primary btn-sm" onClick={() => patchPost(editingPost.id, { title: editingPost.title, content: editingPost.content, published: editingPost.published, featured: editingPost.featured, pinned: editingPost.pinned, locked: editingPost.locked })}>Save</button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Edit Comment Modal */}
      {editingComment && (
        <div className="fixed inset-0 bg-black/40 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="card bg-base-100 w-full max-w-lg">
            <div className="card-body space-y-4">
              <h3 className="card-title text-sm">Edit Comment</h3>
              <p className="text-[10px] opacity-60">Post: {editingComment.post?.title || '—'}</p>
              <textarea className="textarea textarea-bordered textarea-sm h-40" value={editingComment.content} onChange={(e) => setEditingComment({ ...editingComment, content: e.target.value })} />
              <div className="flex justify-end gap-2">
                <button className="btn btn-sm" onClick={() => setEditingComment(null)}>Cancel</button>
                <button className="btn btn-primary btn-sm" onClick={() => patchComment(editingComment.id, { content: editingComment.content })}>Save</button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Edit Community Modal */}
      {editingCommunity && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60] p-4 overflow-y-auto">
          <div className="card bg-base-100 w-full max-w-2xl">
            <div className="card-body space-y-4">
              <div className="flex justify-between items-start">
                <h3 className="card-title text-sm">Edit Community</h3>
                <button className="btn btn-ghost btn-xs" onClick={() => setEditingCommunity(null)}>✕</button>
              </div>
              <div className="flex flex-col md:flex-row gap-6">
                <div className="w-full md:w-1/3 space-y-4">
                  <div className="flex flex-col items-center gap-2">
                    <div className="avatar">
                      <div className="w-28 h-28 rounded-lg bg-base-200 flex items-center justify-center overflow-hidden">
                        {editingCommunity.image ? (
                          <div className="relative w-full h-full">
                            <Image src={editingCommunity.image} alt={editingCommunity.name} fill className="object-cover" sizes="112px" />
                          </div>
                        ) : <span className="text-4xl">🏘️</span>}
                      </div>
                    </div>
                    <label className="btn btn-xs btn-outline relative cursor-pointer">Change Image
                      <input type="file" accept="image/*" className="absolute inset-0 opacity-0 cursor-pointer" onChange={(e) => { const f = e.target.files?.[0]; if (f) handleCommunityImageUpload(f); }} />
                    </label>
                    {isUploadingImage && <span className="loading loading-spinner loading-xs" />}
                  </div>
                  <div className="space-y-2 text-xs">
                    <label className="flex items-center justify-between gap-2">Private
                      <input type="checkbox" className="toggle toggle-xs" checked={!!editingCommunity.isPrivate} onChange={(e)=> setEditingCommunity({ ...editingCommunity, isPrivate: e.target.checked })} />
                    </label>
                    <label className="flex items-center justify-between gap-2">Allow Posts
                      <input type="checkbox" className="toggle toggle-xs" checked={editingCommunity.allowPosts !== false} onChange={(e)=> setEditingCommunity({ ...editingCommunity, allowPosts: e.target.checked })} />
                    </label>
                    <label className="flex items-center justify-between gap-2">Require Approval
                      <input type="checkbox" className="toggle toggle-xs" checked={!!editingCommunity.requireApproval} onChange={(e)=> setEditingCommunity({ ...editingCommunity, requireApproval: e.target.checked })} />
                    </label>
                    <label className="flex items-center justify-between gap-2">Archived
                      <input type="checkbox" className="toggle toggle-xs" checked={!!editingCommunity.archived} onChange={(e)=> setEditingCommunity({ ...editingCommunity, archived: e.target.checked })} />
                    </label>
                  </div>
                </div>
                <div className="flex-1 space-y-3">
                  <div className="form-control">
                    <label className="label py-1"><span className="label-text text-xs">Name</span></label>
                    <input className="input input-bordered input-sm" value={editingCommunity.name} onChange={(e)=> setEditingCommunity({ ...editingCommunity, name: e.target.value })} />
                  </div>
                  <div className="form-control">
                    <label className="label py-1"><span className="label-text text-xs">Slug</span></label>
                    <input className="input input-bordered input-sm font-mono" value={editingCommunity.slug} onChange={(e)=> setEditingCommunity({ ...editingCommunity, slug: e.target.value.toLowerCase().replace(/[^a-z0-9-]/g,'-') })} />
                  </div>
                  <div className="form-control">
                    <label className="label py-1"><span className="label-text text-xs">Description</span></label>
                    <textarea className="textarea textarea-bordered textarea-sm h-32" value={editingCommunity.description} onChange={(e)=> setEditingCommunity({ ...editingCommunity, description: e.target.value })} />
                  </div>
                  <div className="grid grid-cols-2 gap-3 text-[10px] opacity-60">
                    <div><span className="font-semibold">Created:</span> {new Date(editingCommunity.createdAt).toLocaleString()}</div>
                    <div><span className="font-semibold">Updated:</span> {new Date(editingCommunity.updatedAt).toLocaleString()}</div>
                  </div>
                </div>
              </div>
              <div className="flex justify-end gap-2 pt-2">
                <button className="btn btn-sm" onClick={()=> setEditingCommunity(null)}>Cancel</button>
                <button className="btn btn-primary btn-sm" onClick={()=> {
                  const { name, description, slug, isPrivate, allowPosts, requireApproval, archived, image } = editingCommunity;
                  handleUpdateCommunity({ name, description, slug, isPrivate, allowPosts, requireApproval, archived, image });
                }}>Save Changes</button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Edit User Modal */}
      {editingUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60] p-4">
          <div className="card bg-base-100 w-full max-w-md">
            <div className="card-body space-y-3">
              <div className="flex justify-between items-center">
                <h3 className="card-title text-sm">Edit User</h3>
                <button className="btn btn-ghost btn-xs" onClick={()=> setEditingUser(null)}>✕</button>
              </div>
              <input className="input input-bordered input-sm" placeholder="Name" value={editingUser.name || ''} onChange={(e)=> setEditingUser({ ...editingUser, name: e.target.value })} />
              <input className="input input-bordered input-sm" placeholder="Email" value={editingUser.email || ''} onChange={(e)=> setEditingUser({ ...editingUser, email: e.target.value })} />
              <select className="select select-bordered select-sm" value={editingUser.role} onChange={(e)=> setEditingUser({ ...editingUser, role: e.target.value })}>
                <option value="USER">USER</option>
                <option value="EDITOR">EDITOR</option>
                <option value="ADMIN">ADMIN</option>
              </select>
              <textarea className="textarea textarea-bordered textarea-sm h-28" placeholder="Bio" value={editingUser.bio || ''} onChange={(e)=> setEditingUser({ ...editingUser, bio: e.target.value })} />
              <div className="flex justify-end gap-2 pt-2">
                <button className="btn btn-sm" onClick={()=> setEditingUser(null)}>Cancel</button>
                <button className="btn btn-primary btn-sm" disabled={isUpdating} onClick={()=> handleUpdateUser({ name: editingUser.name, email: editingUser.email, role: editingUser.role, bio: editingUser.bio })}>{isUpdating ? <span className="loading loading-spinner loading-xs" /> : 'Save'}</button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Add User Modal */}
      {isAddingUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60] p-4">
          <div className="card bg-base-100 w-full max-w-md">
            <AddUserForm onCancel={()=> setIsAddingUser(false)} onSubmit={handleAddUser} submitting={isUpdating} />
          </div>
        </div>
      )}
    </div>
  );
}

// Lightweight AddUserForm component (in-file to avoid new file creation)
interface AddUserPayload { name: string; email: string; role: string; password?: string }
function AddUserForm({ onCancel, onSubmit, submitting }: { onCancel: () => void; onSubmit: (d: AddUserPayload) => void; submitting: boolean }) {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('USER');
  const [password, setPassword] = useState('');
  return (
    <div className="card-body space-y-3">
      <div className="flex justify-between items-center">
        <h3 className="card-title text-sm">Add User</h3>
        <button className="btn btn-ghost btn-xs" onClick={onCancel}>✕</button>
      </div>
      <input className="input input-bordered input-sm" placeholder="Name" value={name} onChange={(e)=> setName(e.target.value)} />
      <input className="input input-bordered input-sm" placeholder="Email" value={email} onChange={(e)=> setEmail(e.target.value)} />
      <input className="input input-bordered input-sm" type="password" placeholder="Password (optional)" value={password} onChange={(e)=> setPassword(e.target.value)} />
      <select className="select select-bordered select-sm" value={role} onChange={(e)=> setRole(e.target.value)}>
        <option value="USER">USER</option>
        <option value="EDITOR">EDITOR</option>
        <option value="ADMIN">ADMIN</option>
      </select>
              <div className="flex justify-end gap-2 pt-2">
                <button className="btn btn-sm" onClick={onCancel}>Cancel</button>
                <button className="btn btn-primary btn-sm" disabled={submitting} onClick={() => onSubmit({ name, email, role, password: password || undefined })}>{submitting ? <span className="loading loading-spinner loading-xs" /> : 'Create'}</button>
              </div>
            </div>
          );
    }