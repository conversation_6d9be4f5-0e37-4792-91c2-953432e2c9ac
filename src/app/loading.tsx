export default function Loading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-base-100 via-base-200 to-base-300 flex items-center justify-center">
      <div className="container mx-auto px-4 py-8">
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10 max-w-md mx-auto">
          <div className="card-body text-center py-16">
            {/* Animated Logo */}
            <div className="mb-8">
              <img
                src="/images/logo.png"
                alt="COMFOR Logo"
                className="w-16 h-16 mx-auto object-contain animate-pulse"
              />
            </div>

            {/* Loading Spinner */}
            <div className="flex justify-center mb-6">
              <div className="loading loading-spinner loading-lg text-primary"></div>
            </div>

            <p className="text-base-content/60">
              Loading amazing content...
            </p>

            {/* Progress Dots */}
            <div className="flex justify-center space-x-2 mt-6">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-secondary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
