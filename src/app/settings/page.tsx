"use client";

import { useSession } from "next-auth/react";
import { useState } from "react";
import Link from "next/link";

export default function SettingsPage() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("profile");
  const [settings, setSettings] = useState({
    // Profile settings
    displayName: session?.user?.name || "",
    bio: "",
    location: "",
    website: "",
    twitter: "",
    github: "",
    
    // Privacy settings
    profileVisibility: "public",
    showEmail: false,
    showOnlineStatus: true,
    allowDirectMessages: true,
    
    // Notification settings
    emailNotifications: true,
    pushNotifications: true,
    commentNotifications: true,
    likeNotifications: true,
    followNotifications: true,
    communityNotifications: true,
    
    // Appearance settings
    theme: "system",
    language: "en",
    timezone: "UTC",
    
    // Account settings
    twoFactorEnabled: false,
    dataExport: false
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = async () => {
    // TODO: Implement actual save functionality
    console.log("Saving settings:", settings);
  };

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30 flex items-center justify-center">
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10 w-full max-w-md">
          <div className="card-body text-center py-12">
            <div className="text-6xl mb-4">⚙️</div>
            <h2 className="text-2xl font-bold mb-2">Sign In Required</h2>
            <p className="text-base-content/70 mb-6">
              Please sign in to access your settings
            </p>
            <Link href="/auth/signin" className="btn btn-primary btn-lg rounded-full px-8">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              Sign In
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: "profile", label: "Profile", icon: "👤" },
    { id: "privacy", label: "Privacy", icon: "🔒" },
    { id: "notifications", label: "Notifications", icon: "🔔" },
    { id: "appearance", label: "Appearance", icon: "🎨" },
    { id: "account", label: "Account", icon: "⚙️" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200/30 via-base-100 to-base-200/30">
      <div className="max-w-6xl mx-auto space-y-8 p-4">
        {/* Header */}
        <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-2xl border border-primary/10">
          <div className="card-body p-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-2">
                  Settings
                </h1>
                <p className="text-base-content/70 text-lg">
                  Customize your forum experience
                </p>
              </div>
              <Link href="/profile" className="btn btn-ghost rounded-full">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Profile
              </Link>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10 sticky top-4">
              <div className="card-body p-6">
                <ul className="menu menu-vertical space-y-2">
                  {tabs.map((tab) => (
                    <li key={tab.id}>
                      <button
                        onClick={() => setActiveTab(tab.id)}
                        className={`rounded-xl transition-all duration-300 ${
                          activeTab === tab.id
                            ? "bg-gradient-to-r from-primary/10 to-accent/10 text-primary font-medium"
                            : "hover:bg-base-200/50"
                        }`}
                      >
                        <span className="text-lg mr-2">{tab.icon}</span>
                        {tab.label}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="card bg-gradient-to-br from-base-100 to-base-200/50 shadow-xl border border-primary/10">
              <div className="card-body p-8">
                {/* Profile Settings */}
                {activeTab === "profile" && (
                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                      Profile Information
                    </h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Display Name</span>
                        </label>
                        <input
                          type="text"
                          className="input input-bordered"
                          value={settings.displayName}
                          onChange={(e) => handleSettingChange("displayName", e.target.value)}
                          placeholder="Your display name"
                        />
                      </div>

                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Location</span>
                        </label>
                        <input
                          type="text"
                          className="input input-bordered"
                          value={settings.location}
                          onChange={(e) => handleSettingChange("location", e.target.value)}
                          placeholder="City, Country"
                        />
                      </div>

                      <div className="form-control md:col-span-2">
                        <label className="label">
                          <span className="label-text font-medium">Bio</span>
                        </label>
                        <textarea
                          className="textarea textarea-bordered h-24"
                          value={settings.bio}
                          onChange={(e) => handleSettingChange("bio", e.target.value)}
                          placeholder="Tell us about yourself..."
                          maxLength={500}
                        />
                        <label className="label">
                          <span className="label-text-alt">{settings.bio.length}/500 characters</span>
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Website</span>
                        </label>
                        <input
                          type="url"
                          className="input input-bordered"
                          value={settings.website}
                          onChange={(e) => handleSettingChange("website", e.target.value)}
                          placeholder="https://yourwebsite.com"
                        />
                      </div>

                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Twitter</span>
                        </label>
                        <input
                          type="text"
                          className="input input-bordered"
                          value={settings.twitter}
                          onChange={(e) => handleSettingChange("twitter", e.target.value)}
                          placeholder="@username"
                        />
                      </div>

                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">GitHub</span>
                        </label>
                        <input
                          type="text"
                          className="input input-bordered"
                          value={settings.github}
                          onChange={(e) => handleSettingChange("github", e.target.value)}
                          placeholder="username"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Privacy Settings */}
                {activeTab === "privacy" && (
                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                      Privacy & Security
                    </h2>
                    
                    <div className="space-y-4">
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Profile Visibility</span>
                        </label>
                        <select
                          className="select select-bordered"
                          value={settings.profileVisibility}
                          onChange={(e) => handleSettingChange("profileVisibility", e.target.value)}
                        >
                          <option value="public">🌍 Public - Anyone can view</option>
                          <option value="members">👥 Members only</option>
                          <option value="private">🔒 Private - Only you</option>
                        </select>
                      </div>

                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">Show email address</span>
                            <div className="text-sm text-base-content/60">Allow others to see your email</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.showEmail}
                            onChange={(e) => handleSettingChange("showEmail", e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">Show online status</span>
                            <div className="text-sm text-base-content/60">Let others know when you're online</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.showOnlineStatus}
                            onChange={(e) => handleSettingChange("showOnlineStatus", e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">Allow direct messages</span>
                            <div className="text-sm text-base-content/60">Let other users send you private messages</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.allowDirectMessages}
                            onChange={(e) => handleSettingChange("allowDirectMessages", e.target.checked)}
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {/* Notification Settings */}
                {activeTab === "notifications" && (
                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                      Notification Preferences
                    </h2>

                    <div className="space-y-4">
                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">Email notifications</span>
                            <div className="text-sm text-base-content/60">Receive notifications via email</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.emailNotifications}
                            onChange={(e) => handleSettingChange("emailNotifications", e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">Push notifications</span>
                            <div className="text-sm text-base-content/60">Receive browser push notifications</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.pushNotifications}
                            onChange={(e) => handleSettingChange("pushNotifications", e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="divider">Notification Types</div>

                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">Comments on your posts</span>
                            <div className="text-sm text-base-content/60">When someone comments on your posts</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.commentNotifications}
                            onChange={(e) => handleSettingChange("commentNotifications", e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">Likes on your posts</span>
                            <div className="text-sm text-base-content/60">When someone likes your posts or comments</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.likeNotifications}
                            onChange={(e) => handleSettingChange("likeNotifications", e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">New followers</span>
                            <div className="text-sm text-base-content/60">When someone starts following you</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.followNotifications}
                            onChange={(e) => handleSettingChange("followNotifications", e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">Community updates</span>
                            <div className="text-sm text-base-content/60">Updates from communities you've joined</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.communityNotifications}
                            onChange={(e) => handleSettingChange("communityNotifications", e.target.checked)}
                          />
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {/* Appearance Settings */}
                {activeTab === "appearance" && (
                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                      Appearance & Language
                    </h2>

                    <div className="space-y-6">
                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Theme</span>
                        </label>
                        <select
                          className="select select-bordered"
                          value={settings.theme}
                          onChange={(e) => handleSettingChange("theme", e.target.value)}
                        >
                          <option value="system">🖥️ System default</option>
                          <option value="light">☀️ Light mode</option>
                          <option value="dark">🌙 Dark mode</option>
                        </select>
                      </div>

                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Language</span>
                        </label>
                        <select
                          className="select select-bordered"
                          value={settings.language}
                          onChange={(e) => handleSettingChange("language", e.target.value)}
                        >
                          <option value="en">🇺🇸 English</option>
                          <option value="es">🇪🇸 Español</option>
                          <option value="fr">🇫🇷 Français</option>
                          <option value="de">🇩🇪 Deutsch</option>
                          <option value="ja">🇯🇵 日本語</option>
                        </select>
                      </div>

                      <div className="form-control">
                        <label className="label">
                          <span className="label-text font-medium">Timezone</span>
                        </label>
                        <select
                          className="select select-bordered"
                          value={settings.timezone}
                          onChange={(e) => handleSettingChange("timezone", e.target.value)}
                        >
                          <option value="UTC">UTC (Coordinated Universal Time)</option>
                          <option value="America/New_York">Eastern Time (ET)</option>
                          <option value="America/Chicago">Central Time (CT)</option>
                          <option value="America/Denver">Mountain Time (MT)</option>
                          <option value="America/Los_Angeles">Pacific Time (PT)</option>
                          <option value="Europe/London">London (GMT)</option>
                          <option value="Europe/Paris">Paris (CET)</option>
                          <option value="Asia/Tokyo">Tokyo (JST)</option>
                        </select>
                      </div>
                    </div>
                  </div>
                )}

                {/* Account Settings */}
                {activeTab === "account" && (
                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                      Account & Security
                    </h2>

                    <div className="space-y-6">
                      <div className="form-control">
                        <label className="cursor-pointer label">
                          <span className="label-text">
                            <span className="font-medium">Two-factor authentication</span>
                            <div className="text-sm text-base-content/60">Add an extra layer of security to your account</div>
                          </span>
                          <input
                            type="checkbox"
                            className="toggle toggle-primary"
                            checked={settings.twoFactorEnabled}
                            onChange={(e) => handleSettingChange("twoFactorEnabled", e.target.checked)}
                          />
                        </label>
                      </div>

                      <div className="divider">Data Management</div>

                      <div className="space-y-4">
                        <div className="alert alert-info">
                          <svg className="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          <span>You can export your data or delete your account at any time.</span>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-4">
                          <button className="btn btn-outline btn-info rounded-full flex-1">
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Export Data
                          </button>
                          <button className="btn btn-outline btn-error rounded-full flex-1">
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            Delete Account
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Save Button */}
                <div className="divider"></div>
                <div className="flex justify-end">
                  <button
                    onClick={handleSave}
                    className="btn btn-primary rounded-full px-8 shadow-lg"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
