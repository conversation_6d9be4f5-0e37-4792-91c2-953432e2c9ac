import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import bcrypt from 'bcryptjs';

// Validation schema for creating users
const createUserSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  role: z.enum(['USER', 'EDITOR', 'ADMIN']).optional().default('USER'),
  bio: z.string().max(500, 'Bio too long').optional(),
});

// GET /api/users - Get all users with privacy controls
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    const currentUser = session?.user ? await prisma.user.findUnique({
      where: { email: session.user.email! },
    }) : null;

    const isAdmin = currentUser?.role === 'ADMIN';
    const isAuthenticated = !!session?.user;

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const role = searchParams.get('role');

    const skip = (page - 1) * limit;

    // Build where clause with privacy controls
    const where: any = {};

    // Only show public users unless admin
    if (!isAdmin) {
      where.isPrivate = false;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        // Only allow email search for admins
        ...(isAdmin ? [{ email: { contains: search, mode: 'insensitive' } }] : []),
      ];
    }
    if (role && isAdmin) {
      where.role = role;
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          name: true,
          email: isAdmin, // Only include email for admins
          role: isAdmin, // Only include role for admins
          image: true,
          bio: true,
          createdAt: true,
          updatedAt: true,
          isPrivate: isAdmin, // Only include privacy settings for admins
          showEmail: isAdmin,
          _count: {
            select: {
              posts: true,
              comments: true,
              likes: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);

    // Filter sensitive information based on user privacy settings
    const filteredUsers = users.map(user => {
      const publicUser: any = {
        id: user.id,
        name: user.name,
        image: user.image,
        bio: user.bio,
        createdAt: user.createdAt,
        _count: user._count,
      };

      // Add additional fields for admins
      if (isAdmin) {
        publicUser.email = user.email;
        publicUser.role = user.role;
        publicUser.updatedAt = user.updatedAt;
        publicUser.isPrivate = user.isPrivate;
        publicUser.showEmail = user.showEmail;
      } else if (user.showEmail && isAuthenticated) {
        // Show email only if user opted to show it and viewer is authenticated
        publicUser.email = user.email;
      }

      return publicUser;
    });

    return NextResponse.json({
      users: filteredUsers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Find the user in the database to check role
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email! },
    });

    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = createUserSchema.parse(body);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12);

    const user = await prisma.user.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        role: validatedData.role,
        bio: validatedData.bio,
        // Note: In a real app, you'd handle password differently
        // This is just for demo purposes
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        image: true,
        bio: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.issues },
        { status: 400 }
      );
    }

    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
