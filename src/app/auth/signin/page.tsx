"use client";

import { useState, Suspense } from "react";
import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";

function SignInForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  // useSearchParams should always return an object, but guard defensively in case of framework typing edge cases
  const callbackUrl = searchParams?.get("callbackUrl") ?? "/";
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const result = await signIn("credentials", {
        email: formData.email,
        password: formData.password,
        redirect: false,
      });

      if (result?.error) {
        setError("Invalid credentials. Please try again.");
      } else {
        router.push(callbackUrl);
      }
    } catch (error) {
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleOAuthSignIn = async (provider: string) => {
    setIsLoading(true);
    try {
      await signIn(provider, { callbackUrl });
    } catch (error) {
      setError("An error occurred with OAuth sign in.");
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-base-200 via-base-300 to-base-200 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-accent/5"></div>
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-20 left-20 w-32 h-32 bg-primary/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-accent/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-secondary/5 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-md w-full space-y-8 relative z-10">
        <div className="text-center">
          <div className="mb-8">
            <div className="flex flex-col items-center gap-4 mb-6">
              <img
                src="/images/logo.png"
                alt="COMFOR Logo"
                className="w-20 h-20 object-contain drop-shadow-2xl"
              />
            </div>
            <h2 className="text-3xl font-black bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
              Welcome Back
            </h2>
            <p className="mt-3 text-lg text-base-content/70">
              Sign in to continue your journey with our community
            </p>
          </div>
        </div>

        <div className="card bg-base-100/80 backdrop-blur-xl shadow-2xl border border-primary/10">
          <div className="card-body p-8">
            {error && (
              <div className="alert alert-error mb-6 rounded-2xl border border-error/20 bg-error/10">
                <svg className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="font-medium">{error}</span>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold text-base">Email Address</span>
                </label>
                <div className="relative">
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter your email address"
                    className="input input-bordered w-full rounded-2xl bg-base-100/50 border-primary/20 focus:border-primary/40 focus:bg-base-100 transition-all duration-300 pl-12"
                    required
                  />
                  <svg className="w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                </div>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text font-semibold text-base">Password</span>
                </label>
                <div className="relative">
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    placeholder="Enter your password"
                    className="input input-bordered w-full rounded-2xl bg-base-100/50 border-primary/20 focus:border-primary/40 focus:bg-base-100 transition-all duration-300 pl-12"
                    required
                  />
                  <svg className="w-5 h-5 absolute left-4 top-1/2 transform -translate-y-1/2 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <label className="label">
                  <Link href="/auth/forgot-password" className="label-text-alt link link-hover text-primary hover:text-primary/80 transition-colors duration-300">
                    Forgot your password?
                  </Link>
                </label>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="btn btn-primary w-full rounded-2xl text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-[1.02] disabled:hover:scale-100"
              >
                {isLoading ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Signing you in...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Sign In to Forum
                  </>
                )}
              </button>
            </form>

            <div className="divider text-base-content/50 font-medium">OR CONTINUE WITH</div>

            {/* OAuth Providers */}
            <div className="space-y-4">
              <button
                onClick={() => handleOAuthSignIn("google")}
                disabled={isLoading}
                className="btn btn-outline w-full rounded-2xl border-2 hover:bg-base-100 hover:border-primary/40 transition-all duration-300 hover:scale-[1.02] text-base font-medium"
              >
                <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Continue with Google
              </button>

              <button
                onClick={() => handleOAuthSignIn("github")}
                disabled={isLoading}
                className="btn btn-outline w-full rounded-2xl border-2 hover:bg-base-100 hover:border-primary/40 transition-all duration-300 hover:scale-[1.02] text-base font-medium"
              >
                <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
                Continue with GitHub
              </button>
            </div>

            <div className="text-center mt-8">
              <p className="text-base text-base-content/70">
                Don&apos;t have an account?{" "}
                <Link href="/auth/register" className="link link-primary font-semibold hover:text-primary/80 transition-colors duration-300">
                  Create one now
                </Link>
              </p>
            </div>
          </div>
        </div>

        {/* Demo Accounts */}
        <div className="card bg-gradient-to-br from-info/10 to-accent/10 backdrop-blur-xl shadow-2xl border border-info/20">
          <div className="card-body p-6">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-info/20 rounded-xl mr-3">
                <svg className="w-5 h-5 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-bold text-lg text-info">Demo Accounts Available</h3>
            </div>
            <div className="grid grid-cols-1 gap-3">
              <div className="p-3 bg-base-100/50 rounded-xl border border-error/20">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold text-error">Admin Account</p>
                    <p className="text-sm text-base-content/70"><EMAIL></p>
                  </div>
                  <div className="badge badge-error badge-outline">ADMIN</div>
                </div>
              </div>
              <div className="p-3 bg-base-100/50 rounded-xl border border-warning/20">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold text-warning">Editor Account</p>
                    <p className="text-sm text-base-content/70"><EMAIL></p>
                  </div>
                  <div className="badge badge-warning badge-outline">EDITOR</div>
                </div>
              </div>
              <div className="p-3 bg-base-100/50 rounded-xl border border-info/20">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold text-info">User Account</p>
                    <p className="text-sm text-base-content/70"><EMAIL></p>
                  </div>
                  <div className="badge badge-info badge-outline">USER</div>
                </div>
              </div>
            </div>
            <div className="mt-4 p-3 bg-success/10 rounded-xl border border-success/20">
              <p className="text-sm text-success font-medium text-center">
                💡 Use any password - Demo mode is active
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function SignInPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center"><div className="loading loading-spinner loading-lg"></div></div>}>
      <SignInForm />
    </Suspense>
  );
}
